using System.Net.Http;
using Newtonsoft.Json;
using CryptoDeviationViewer.Models;

namespace CryptoDeviationViewer.Services
{
    /// <summary>
    /// 市场数据服务，负责从Binance API获取和处理数据
    /// </summary>
    public class MarketDataService
    {
        private readonly HttpClient _httpClient;
        private const string PREMIUM_URL = "https://fapi.binance.com/fapi/v1/premiumIndex";
        private const string PRICE_URL = "https://fapi.binance.com/fapi/v1/ticker/price";

        // 调试模式开关
        private const bool ENABLE_CSV_EXPORT = true;

        public MarketDataService()
        {
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromSeconds(10)
            };
        }

        /// <summary>
        /// 获取市场数据并计算偏差
        /// </summary>
        /// <returns>市场分析结果</returns>
        public async Task<MarketAnalysis?> GetMarketAnalysisAsync()
        {
            try
            {
                // 并行获取两个API的数据
                var premiumTask = GetPremiumDataAsync();
                var priceTask = GetPriceDataAsync();

                await Task.WhenAll(premiumTask, priceTask);

                var premiumData = await premiumTask;
                var priceData = await priceTask;

                if (premiumData == null || priceData == null)
                {
                    return null;
                }

                // 导出原始数据用于调试
                if (ENABLE_CSV_EXPORT)
                {
                    var timestamp = DateTime.Now;
                    CsvExportService.ExportPremiumData(premiumData, timestamp);
                    CsvExportService.ExportPriceData(priceData, timestamp);
                }

                return CalculateDeviations(premiumData, priceData);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取市场数据失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取Premium Index数据
        /// </summary>
        private async Task<List<PremiumIndexData>?> GetPremiumDataAsync()
        {
            try
            {
                var response = await _httpClient.GetStringAsync(PREMIUM_URL);
                return JsonConvert.DeserializeObject<List<PremiumIndexData>>(response);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取Premium数据失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取Price数据
        /// </summary>
        private async Task<List<PriceData>?> GetPriceDataAsync()
        {
            try
            {
                var response = await _httpClient.GetStringAsync(PRICE_URL);
                return JsonConvert.DeserializeObject<List<PriceData>>(response);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取Price数据失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 计算价格偏差
        /// </summary>
        private MarketAnalysis CalculateDeviations(List<PremiumIndexData> premiumData, List<PriceData> priceData)
        {
            var timestamp = DateTime.Now;
            var deviationPoints = new List<DeviationPoint>();
            var filterLog = new List<string>(); // 用于记录过滤日志

            // 过滤掉estimatedSettlePrice为0的数据
            var validPremiumData = premiumData.Where(p => p.EstimatedSettlePrice != "0").ToList();
            filterLog.Add($"原始Premium数据: {premiumData.Count} 条");
            filterLog.Add($"过滤EstimatedSettlePrice=0后: {validPremiumData.Count} 条");

            // 获取当前UTC时间，用于过滤过期数据
            var currentUtcTime = DateTime.UtcNow;
            var oneHourAgo = currentUtcTime.AddHours(-1);
            filterLog.Add($"当前UTC时间: {currentUtcTime:yyyy-MM-dd HH:mm:ss}");
            filterLog.Add($"过滤时间阈值: {oneHourAgo:yyyy-MM-dd HH:mm:ss} (1小时前)");

            // 合并数据并计算偏差
            int expiredPremiumCount = 0;
            int expiredPriceCount = 0;
            int usdtFilteredCount = 0;
            int invalidPriceCount = 0;
            int processedCount = 0;

            foreach (var premium in validPremiumData)
            {
                var price = priceData.FirstOrDefault(p => p.Symbol == premium.Symbol);
                if (price == null) continue;

                // 检查Premium Index数据时间戳是否在有效范围内（1小时内）
                var premiumTimestamp = DateTimeOffset.FromUnixTimeMilliseconds(premium.Time).UtcDateTime;
                if (premiumTimestamp < oneHourAgo)
                {
                    expiredPremiumCount++;
                    filterLog.Add($"跳过过期Premium数据: {premium.Symbol}, 时间戳: {premiumTimestamp:yyyy-MM-dd HH:mm:ss} UTC");
                    continue;
                }

                // 检查Price数据时间戳是否在有效范围内（1小时内）
                if (price.Time > 0)
                {
                    var priceTimestamp = DateTimeOffset.FromUnixTimeMilliseconds(price.Time).UtcDateTime;
                    if (priceTimestamp < oneHourAgo)
                    {
                        expiredPriceCount++;
                        filterLog.Add($"跳过过期Price数据: {premium.Symbol}, 时间戳: {priceTimestamp:yyyy-MM-dd HH:mm:ss} UTC");
                        continue;
                    }
                }

                if (double.TryParse(price.Price, out double priceValue) &&
                    double.TryParse(premium.IndexPrice, out double indexPriceValue) &&
                    indexPriceValue > 0)
                {
                    // 过滤掉包含USDT_的symbol
                    if (premium.Symbol.Contains("USDT_"))
                    {
                        usdtFilteredCount++;
                        continue;
                    }

                    var deviation = (priceValue / indexPriceValue) - 1;

                    deviationPoints.Add(new DeviationPoint
                    {
                        Symbol = premium.Symbol,
                        Timestamp = timestamp,
                        Deviation = deviation,
                        Price = priceValue,
                        IndexPrice = indexPriceValue
                    });
                    processedCount++;
                }
                else
                {
                    invalidPriceCount++;
                }
            }

            // 添加过滤统计到日志
            filterLog.Add($"过期Premium数据过滤: {expiredPremiumCount} 条");
            filterLog.Add($"过期Price数据过滤: {expiredPriceCount} 条");
            filterLog.Add($"USDT_符号过滤: {usdtFilteredCount} 条");
            filterLog.Add($"无效价格数据: {invalidPriceCount} 条");
            filterLog.Add($"成功处理: {processedCount} 条");
            filterLog.Add($"最终偏差数据点: {deviationPoints.Count} 条");

            // 导出过滤日志
            if (ENABLE_CSV_EXPORT)
            {
                CsvExportService.ExportFilterLog(filterLog, timestamp);
            }

            // 计算统计数据
            if (deviationPoints.Count == 0)
            {
                return new MarketAnalysis
                {
                    Timestamp = timestamp,
                    DeviationPoints = deviationPoints
                };
            }

            var deviations = deviationPoints.Select(p => p.Deviation).ToList();

            var analysis = new MarketAnalysis
            {
                Timestamp = timestamp,
                TotalSymbols = deviationPoints.Count,
                AverageDeviation = deviations.Average(),
                StandardDeviation = CalculateStandardDeviation(deviations),
                MaxDeviation = deviations.Max(),
                MinDeviation = deviations.Min(),
                PositiveDeviations = deviations.Count(d => d > 0),
                NegativeDeviations = deviations.Count(d => d < 0),
                DeviationPoints = deviationPoints
            };

            // 导出市场分析结果
            if (ENABLE_CSV_EXPORT)
            {
                CsvExportService.ExportMarketAnalysis(analysis);
            }

            return analysis;
        }

        /// <summary>
        /// 计算标准差
        /// </summary>
        private static double CalculateStandardDeviation(List<double> values)
        {
            if (values.Count <= 1) return 0;

            var average = values.Average();
            var sumOfSquares = values.Sum(d => Math.Pow(d - average, 2));
            return Math.Sqrt(sumOfSquares / (values.Count - 1));
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
