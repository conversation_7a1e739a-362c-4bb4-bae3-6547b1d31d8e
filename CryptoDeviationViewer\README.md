# 加密货币偏差监控器 (CryptoDeviationViewer)

一个实时监控加密货币价格偏差的WPF应用程序，专为极端市场条件下的交易决策辅助而设计。

## 项目概述

这是一个基于.NET 9.0和WPF的桌面应用程序，用于监控加密货币期货价格与指数价格之间的偏差。在正常市场条件下，大多数数据聚集在0%附近，但在极端市场条件下（5-15%的偏差），该工具能够快速识别符号溢价，为交易决策提供重要参考。

## 核心功能

### 📊 实时数据监控
- **数据源**: Binance API (Premium Index 和 Price API)
- **更新频率**: 每2秒自动更新
- **数据容量**: 保持30个历史数据点（60秒历史记录）
- **性能优化**: 限制散点图最大2000个数据点

### 📈 动态图表显示
- **图表引擎**: ScottPlot 5.0.55
- **显示方式**: 散点图，按偏差值分组着色
- **流动效果**: 新数据从右侧进入，历史数据向左移动
- **颜色映射**: 
  - 绿色渐变：正偏差 (0% → +10%)
  - 红色渐变：负偏差 (0% → -10%)
  - 白色：接近零偏差
  - 黄色基准线：零偏差线 (#FCD535, 2px粗细)

### 🎯 智能Y轴缩放
- **正常模式**: ±2% 范围
- **警告模式**: ±5% 范围（检测到异常时）
- **扩展模式**: ±15% 最大范围（极端市场条件）
- **自动回归**: 市场平静时逐渐回到正常范围

### 🎮 交互功能
- **流式播放模式**: 新数据出现在倒数第二个位置，最右侧保留空白
- **缩放保持**: 用户手动缩放后，新数据更新时保持缩放状态
- **重置缩放**: 一键恢复默认视图
- **测试模式**: 使用300个模拟数据点进行功能验证

### 🌙 暗黑主题
- **主色调**: #1E1E1E (深灰背景)
- **边框**: #3F3F46 (中灰边框)
- **状态栏**: #2D2D30 (深灰状态栏)
- **文字**: 白色主文字，彩色状态指示

## 技术架构

### 依赖包
```xml
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="ScottPlot.WPF" Version="5.0.55" />
```

### 项目结构
```
CryptoDeviationViewer/
├── Models/
│   └── MarketData.cs          # 数据模型定义
├── Services/
│   ├── MarketDataService.cs   # 实时数据服务
│   ├── TestDataService.cs     # 测试数据服务
│   └── CsvExportService.cs    # 数据导出服务
├── ViewModels/
│   └── MainViewModel.cs       # 主视图模型
├── Converters/
│   ├── BooleanToVisibilityConverter.cs
│   └── ExtremeCountToColorConverter.cs
├── Commands/
│   └── RelayCommand.cs        # 命令实现
├── MainWindow.xaml            # 主窗口界面
└── App.xaml                   # 应用程序入口
```

### 核心类说明

#### MarketAnalysis
市场分析结果模型，包含：
- 总交易对数量
- 平均偏差和标准差
- 正负偏差统计
- 偏差数据点列表

#### MainViewModel
主要业务逻辑，负责：
- 数据获取和处理
- 图表更新和渲染
- 用户交互响应
- 线程安全管理

## 使用说明

### 启动应用
1. 确保网络连接正常（需要访问Binance API）
2. 运行 `CryptoDeviationViewer.exe`
3. 应用程序将自动开始获取实时数据

### 界面操作
- **测试模式**: 点击右上角"测试模式"按钮切换到模拟数据
- **流式播放**: 点击"流式播放"按钮启用特殊显示模式
- **重置缩放**: 点击"重置缩放"恢复默认视图
- **手动缩放**: 使用鼠标滚轮或拖拽进行图表缩放

### 状态指示
- **绿色圆点**: 应用程序正在运行
- **异常计数**: 显示当前超过3%偏差的交易对数量
- **Y轴模式**: 显示当前的缩放模式和范围

## 测试模式

测试模式提供300个模拟数据点，包含各种极端波动情况：
- 正常波动：±2%范围内
- 中等波动：±5%范围内
- 极端波动：±15%范围内
- 混合场景：多种波动类型组合

## 性能优化

- **线程安全**: 使用锁机制防止并发修改异常
- **数据限制**: 限制历史数据点和散点图数量
- **渐进透明度**: 历史数据使用渐变透明度减少视觉干扰
- **智能刷新**: 避免不必要的图表重绘

## 开发环境

- **.NET**: 9.0-windows
- **UI框架**: WPF
- **图表库**: ScottPlot 5.0.55
- **JSON处理**: Newtonsoft.Json 13.0.3
- **开发工具**: Visual Studio 2022

## 注意事项

1. **网络依赖**: 应用程序需要稳定的网络连接访问Binance API
2. **API限制**: 请遵守Binance API的使用限制和频率要求
3. **数据准确性**: 实时数据可能存在延迟，仅供参考使用
4. **投资风险**: 本工具仅为数据展示，不构成投资建议

## 许可证

本项目仅供学习和研究使用。
