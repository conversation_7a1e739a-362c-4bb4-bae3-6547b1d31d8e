{"version": 3, "targets": {"net9.0-windows7.0": {"HarfBuzzSharp/7.3.0.3": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0.3", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0.3"}, "compile": {"lib/net6.0/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp.NativeAssets.Linux/7.3.0.3": {"type": "package", "dependencies": {"HarfBuzzSharp": "7.3.0.3"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-x64"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.3": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.3": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.NETCore.Platforms/3.1.9": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.SystemEvents/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "OpenTK/4.3.0": {"type": "package", "dependencies": {"OpenTK.Compute": "4.3.0", "OpenTK.Core": "4.3.0", "OpenTK.Graphics": "4.3.0", "OpenTK.Input": "4.3.0", "OpenTK.Mathematics": "4.3.0", "OpenTK.OpenAL": "4.3.0", "OpenTK.Windowing.Common": "4.3.0", "OpenTK.Windowing.Desktop": "4.3.0", "OpenTK.Windowing.GraphicsLibraryFramework": "4.3.0"}}, "OpenTK.Compute/4.3.0": {"type": "package", "compile": {"lib/netcoreapp3.1/OpenTK.Compute.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/OpenTK.Compute.dll": {"related": ".pdb;.xml"}}}, "OpenTK.Core/4.3.0": {"type": "package", "compile": {"lib/netstandard2.1/OpenTK.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/OpenTK.Core.dll": {"related": ".pdb;.xml"}}}, "OpenTK.GLWpfControl/4.2.3": {"type": "package", "dependencies": {"OpenTK": "[4.3.0, 5.0.0)"}, "compile": {"lib/netcoreapp3.1/GLWpfControl.dll": {"related": ".pdb"}}, "runtime": {"lib/netcoreapp3.1/GLWpfControl.dll": {"related": ".pdb"}}}, "OpenTK.Graphics/4.3.0": {"type": "package", "dependencies": {"OpenTK.Core": "[4.3.0, 4.4.0)", "OpenTK.Mathematics": "[4.3.0, 4.4.0)"}, "compile": {"lib/netstandard2.1/OpenTK.Graphics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/OpenTK.Graphics.dll": {"related": ".pdb;.xml"}}}, "OpenTK.Input/4.3.0": {"type": "package", "compile": {"lib/netstandard2.0/OpenTK.Input.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/OpenTK.Input.dll": {"related": ".pdb;.xml"}}}, "OpenTK.Mathematics/4.3.0": {"type": "package", "compile": {"lib/netstandard2.1/OpenTK.Mathematics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/OpenTK.Mathematics.dll": {"related": ".pdb;.xml"}}}, "OpenTK.OpenAL/4.3.0": {"type": "package", "dependencies": {"OpenTK.Core": "[4.3.0, 4.4.0)", "OpenTK.Mathematics": "[4.3.0, 4.4.0)"}, "compile": {"lib/netcoreapp3.1/OpenTK.OpenAL.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/OpenTK.OpenAL.dll": {"related": ".pdb;.xml"}}}, "OpenTK.redist.glfw/3.3.0-pre20200830200122": {"type": "package", "runtimeTargets": {"runtimes/linux-x64/native/libglfw.so.3.3": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-x64/native/libglfw.3.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-x64/native/glfw3.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/glfw3.dll": {"assetType": "native", "rid": "win-x86"}}}, "OpenTK.Windowing.Common/4.3.0": {"type": "package", "dependencies": {"OpenTK.Core": "[4.3.0, 4.4.0)", "OpenTK.Mathematics": "[4.3.0, 4.4.0)"}, "compile": {"lib/netstandard2.1/OpenTK.Windowing.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/OpenTK.Windowing.Common.dll": {"related": ".pdb;.xml"}}}, "OpenTK.Windowing.Desktop/4.3.0": {"type": "package", "dependencies": {"OpenTK.Core": "[4.3.0, 4.4.0)", "OpenTK.Mathematics": "[4.3.0, 4.4.0)", "OpenTK.Windowing.Common": "[4.3.0, 4.4.0)", "OpenTK.Windowing.GraphicsLibraryFramework": "[4.3.0, 4.4.0)"}, "compile": {"lib/netcoreapp3.1/OpenTK.Windowing.Desktop.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/OpenTK.Windowing.Desktop.dll": {"related": ".pdb;.xml"}}}, "OpenTK.Windowing.GraphicsLibraryFramework/4.3.0": {"type": "package", "dependencies": {"OpenTK.Core": "[4.3.0, 4.4.0)", "OpenTK.redist.glfw": "[3.3.0-pre20200830200122, 3.4.0-pre)"}, "compile": {"lib/netcoreapp3.1/OpenTK.Windowing.GraphicsLibraryFramework.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/OpenTK.Windowing.GraphicsLibraryFramework.dll": {"related": ".pdb;.xml"}}}, "ScottPlot/5.0.55": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Linux": "7.3.0.3", "SkiaSharp": "2.88.9", "SkiaSharp.HarfBuzz": "2.88.9", "SkiaSharp.NativeAssets.Linux.NoDependencies": "2.88.9"}, "compile": {"lib/net8.0/ScottPlot.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/ScottPlot.dll": {"related": ".xml"}}}, "ScottPlot.WPF/5.0.55": {"type": "package", "dependencies": {"OpenTK": "4.3.0", "OpenTK.GLWpfControl": "4.2.3", "ScottPlot": "5.0.55", "SkiaSharp.Views.WPF": "2.88.9"}, "compile": {"lib/net6.0-windows7.0/ScottPlot.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows7.0/ScottPlot.WPF.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "SkiaSharp/2.88.9": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9"}, "compile": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.HarfBuzz/2.88.9": {"type": "package", "dependencies": {"HarfBuzzSharp": "7.3.0.3", "SkiaSharp": "2.88.9"}, "compile": {"lib/net6.0/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.HarfBuzz.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.9": {"type": "package", "dependencies": {"SkiaSharp": "2.88.9"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-x64"}}}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "SkiaSharp.Views.Desktop.Common/2.88.9": {"type": "package", "dependencies": {"SkiaSharp": "2.88.9", "System.Drawing.Common": "4.7.3"}, "compile": {"lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.Views.WPF/2.88.9": {"type": "package", "dependencies": {"SkiaSharp": "2.88.9", "SkiaSharp.Views.Desktop.Common": "2.88.9"}, "compile": {"lib/netcoreapp3.1/SkiaSharp.Views.WPF.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/SkiaSharp.Views.WPF.dll": {"related": ".pdb;.xml"}}}, "System.Drawing.Common/4.7.3": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.9", "Microsoft.Win32.SystemEvents": "4.7.0"}, "compile": {"ref/netcoreapp3.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Drawing.Common.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"HarfBuzzSharp/7.3.0.3": {"sha512": "Hq+5+gx10coOvuRgB13KBwiWxJq1QeYuhtVLbA01ZCWaugOnolUahF44KvrQTUUHDNk/C7HB6SMaebsZeOdhgg==", "type": "package", "path": "harfbuzzsharp/7.3.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "harfbuzzsharp.7.3.0.3.nupkg.sha512", "harfbuzzsharp.nuspec", "lib/monoandroid1.0/HarfBuzzSharp.dll", "lib/monoandroid1.0/HarfBuzzSharp.pdb", "lib/monoandroid1.0/HarfBuzzSharp.xml", "lib/net462/HarfBuzzSharp.dll", "lib/net462/HarfBuzzSharp.pdb", "lib/net462/HarfBuzzSharp.xml", "lib/net6.0-android30.0/HarfBuzzSharp.dll", "lib/net6.0-android30.0/HarfBuzzSharp.pdb", "lib/net6.0-android30.0/HarfBuzzSharp.xml", "lib/net6.0-ios13.6/HarfBuzzSharp.dll", "lib/net6.0-ios13.6/HarfBuzzSharp.pdb", "lib/net6.0-ios13.6/HarfBuzzSharp.xml", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.dll", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.pdb", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.xml", "lib/net6.0-macos10.15/HarfBuzzSharp.dll", "lib/net6.0-macos10.15/HarfBuzzSharp.pdb", "lib/net6.0-macos10.15/HarfBuzzSharp.xml", "lib/net6.0-tvos13.4/HarfBuzzSharp.dll", "lib/net6.0-tvos13.4/HarfBuzzSharp.pdb", "lib/net6.0-tvos13.4/HarfBuzzSharp.xml", "lib/net6.0/HarfBuzzSharp.dll", "lib/net6.0/HarfBuzzSharp.pdb", "lib/net6.0/HarfBuzzSharp.xml", "lib/netcoreapp3.1/HarfBuzzSharp.dll", "lib/netcoreapp3.1/HarfBuzzSharp.pdb", "lib/netcoreapp3.1/HarfBuzzSharp.xml", "lib/netstandard1.3/HarfBuzzSharp.dll", "lib/netstandard1.3/HarfBuzzSharp.pdb", "lib/netstandard1.3/HarfBuzzSharp.xml", "lib/netstandard2.0/HarfBuzzSharp.dll", "lib/netstandard2.0/HarfBuzzSharp.pdb", "lib/netstandard2.0/HarfBuzzSharp.xml", "lib/netstandard2.1/HarfBuzzSharp.dll", "lib/netstandard2.1/HarfBuzzSharp.pdb", "lib/netstandard2.1/HarfBuzzSharp.xml", "lib/tizen40/HarfBuzzSharp.dll", "lib/tizen40/HarfBuzzSharp.pdb", "lib/tizen40/HarfBuzzSharp.xml", "lib/uap10.0.10240/HarfBuzzSharp.dll", "lib/uap10.0.10240/HarfBuzzSharp.pdb", "lib/uap10.0.10240/HarfBuzzSharp.xml", "lib/uap10.0.16299/HarfBuzzSharp.dll", "lib/uap10.0.16299/HarfBuzzSharp.pdb", "lib/uap10.0.16299/HarfBuzzSharp.xml", "lib/xamarinios1.0/HarfBuzzSharp.dll", "lib/xamarinios1.0/HarfBuzzSharp.pdb", "lib/xamarinios1.0/HarfBuzzSharp.xml", "lib/xamarinmac2.0/HarfBuzzSharp.dll", "lib/xamarinmac2.0/HarfBuzzSharp.pdb", "lib/xamarinmac2.0/HarfBuzzSharp.xml", "lib/xamarintvos1.0/HarfBuzzSharp.dll", "lib/xamarintvos1.0/HarfBuzzSharp.pdb", "lib/xamarintvos1.0/HarfBuzzSharp.xml", "lib/xamarinwatchos1.0/HarfBuzzSharp.dll", "lib/xamarinwatchos1.0/HarfBuzzSharp.pdb", "lib/xamarinwatchos1.0/HarfBuzzSharp.xml"]}, "HarfBuzzSharp.NativeAssets.Linux/7.3.0.3": {"sha512": "hkcHeTfOyIeJuPtO/QfoqkDvV/MXebZYaA/Bn/S+nXsjH3Wt9oQ6okH2kklYO+1UUdBSJFd67bi9IrpQXI2mPw==", "type": "package", "path": "harfbuzzsharp.nativeassets.linux/7.3.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.Linux.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Linux.targets", "harfbuzzsharp.nativeassets.linux.7.3.0.3.nupkg.sha512", "harfbuzzsharp.nativeassets.linux.nuspec", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/linux-arm/native/libHarfBuzzSharp.so", "runtimes/linux-arm64/native/libHarfBuzzSharp.so", "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so", "runtimes/linux-x64/native/libHarfBuzzSharp.so"]}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.3": {"sha512": "UAwIYnkbBTzBJv1Id8FijY/i8QiIepRemSXufU8fyzwWhYJdx4+ajG8yQUie5HW/uusbVLFSr26muSlJOFDgSw==", "type": "package", "path": "harfbuzzsharp.nativeassets.macos/7.3.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "harfbuzzsharp.nativeassets.macos.7.3.0.3.nupkg.sha512", "harfbuzzsharp.nativeassets.macos.nuspec", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libHarfBuzzSharp.dylib"]}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.3": {"sha512": "RPxRXD16KtSs8Yxr2RK9Qs7AwyN9MlpqZIYs0AvfaJwl7RAtVhC0+u2f2SKwX0uMYYd3O98Z+OBA1sj6aWVKQA==", "type": "package", "path": "harfbuzzsharp.nativeassets.win32/7.3.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "harfbuzzsharp.nativeassets.win32.7.3.0.3.nupkg.sha512", "harfbuzzsharp.nativeassets.win32.nuspec", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libHarfBuzzSharp.dll", "runtimes/win-x64/native/libHarfBuzzSharp.dll", "runtimes/win-x86/native/libHarfBuzzSharp.dll"]}, "Microsoft.NETCore.Platforms/3.1.9": {"sha512": "e+/Br<PERSON>yHoMojWlbcPJAFcShpk3JYvsMfrmoM26dnvHARWR6vtmGbfHppZcANVqf7DUBDIRxlZXcWg7v/9e1TQ==", "type": "package", "path": "microsoft.netcore.platforms/3.1.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.9.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.SystemEvents/4.7.0": {"sha512": "mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "type": "package", "path": "microsoft.win32.systemevents/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Win32.SystemEvents.dll", "lib/net461/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.4.7.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "ref/net461/Microsoft.Win32.SystemEvents.dll", "ref/net461/Microsoft.Win32.SystemEvents.xml", "ref/net472/Microsoft.Win32.SystemEvents.dll", "ref/net472/Microsoft.Win32.SystemEvents.xml", "ref/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "ref/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp2.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp2.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "OpenTK/4.3.0": {"sha512": "w2oiuLDpZbSCyqzHEFFLTO6JMNadHWS6Pp68U68lfzdTrzTQQqyH7x+/PJidsk+wRSDopaH9e03/KzYB9Fs+7Q==", "type": "package", "path": "opentk/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "opentk.4.3.0.nupkg.sha512", "opentk.nuspec"]}, "OpenTK.Compute/4.3.0": {"sha512": "XrMVjBPp1PNuz7z7hdNyO2oBsAPZHcHzILs7ewtEP09CM1KPVwZDAsIjOjEJesLCWrNr+jm6+3TvHActYkk4lQ==", "type": "package", "path": "opentk.compute/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/OpenTK.Compute.dll", "lib/netcoreapp3.1/OpenTK.Compute.pdb", "lib/netcoreapp3.1/OpenTK.Compute.xml", "opentk.compute.4.3.0.nupkg.sha512", "opentk.compute.nuspec"]}, "OpenTK.Core/4.3.0": {"sha512": "Hu+4U+tvnQW++y8Gj4OggLVuhclLm1hVN9ZIc3e9d8uo15CWYOSycw+FQhaRH3aibFv1vFYgH+bMIHM509YwYg==", "type": "package", "path": "opentk.core/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.1/OpenTK.Core.dll", "lib/netstandard2.1/OpenTK.Core.pdb", "lib/netstandard2.1/OpenTK.Core.xml", "opentk.core.4.3.0.nupkg.sha512", "opentk.core.nuspec"]}, "OpenTK.GLWpfControl/4.2.3": {"sha512": "VIq15MqeMB42v7cuoPcbHAFzdlOcN5DZEhguuQVdvZP0NlfajjaoVDqtP3koOWLs1how2YqCaVk1qIvsuRdQqQ==", "type": "package", "path": "opentk.glwpfcontrol/4.2.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/GLWpfControl.dll", "lib/netcoreapp3.1/GLWpfControl.pdb", "opentk.glwpfcontrol.4.2.3.nupkg.sha512", "opentk.glwpfcontrol.nuspec"]}, "OpenTK.Graphics/4.3.0": {"sha512": "N1XXKrX+op6ORxkRoLa8mb/rMQVTW4YsJraneyU0RKqZ04PeOwcnX6BFb+mJISZnbtk+3SyYlWLjSsQjDdFbRg==", "type": "package", "path": "opentk.graphics/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.1/OpenTK.Graphics.dll", "lib/netstandard2.1/OpenTK.Graphics.pdb", "lib/netstandard2.1/OpenTK.Graphics.xml", "opentk.graphics.4.3.0.nupkg.sha512", "opentk.graphics.nuspec"]}, "OpenTK.Input/4.3.0": {"sha512": "tvTQlC87bnNWN5BPOxCqSE2UJ6RKz/r342I25XBfeF/TNpw9Ad6upLdok8l8Nhess9LUItOPT6uBBdOlMEO0cA==", "type": "package", "path": "opentk.input/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/OpenTK.Input.dll", "lib/netstandard2.0/OpenTK.Input.pdb", "lib/netstandard2.0/OpenTK.Input.xml", "opentk.input.4.3.0.nupkg.sha512", "opentk.input.nuspec"]}, "OpenTK.Mathematics/4.3.0": {"sha512": "3yiHBaEeV6OVu7La6961Jx7xoP7dJoZXcRJaDqqPrEmmIuwUx2i4AQI60UpGmcrMJ11QhicmW6zjb8aZYySYrw==", "type": "package", "path": "opentk.mathematics/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.1/OpenTK.Mathematics.dll", "lib/netstandard2.1/OpenTK.Mathematics.pdb", "lib/netstandard2.1/OpenTK.Mathematics.xml", "opentk.mathematics.4.3.0.nupkg.sha512", "opentk.mathematics.nuspec"]}, "OpenTK.OpenAL/4.3.0": {"sha512": "zDzP8wsppS/5jwW8RUaac/bWOrZV5rB8Faejy5vLVE1ixWKclOEyO30ADOO3XZ+IWNKioX5wMhzKcy8kfsKKew==", "type": "package", "path": "opentk.openal/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/OpenTK.OpenAL.dll", "lib/netcoreapp3.1/OpenTK.OpenAL.pdb", "lib/netcoreapp3.1/OpenTK.OpenAL.xml", "opentk.openal.4.3.0.nupkg.sha512", "opentk.openal.nuspec"]}, "OpenTK.redist.glfw/3.3.0-pre20200830200122": {"sha512": "EKE9pJCFHScbqOv/t0zG1h5Eh24nWr44SXYnpYtAWDhJhzAoUoI8TwDRXGvTGhDeCBp8ONEaVsq9xRC+OQIxQA==", "type": "package", "path": "opentk.redist.glfw/3.3.0-pre20200830200122", "files": [".nupkg.metadata", ".signature.p7s", "COPYING.md", "opentk.png", "opentk.redist.glfw.3.3.0-pre20200830200122.nupkg.sha512", "opentk.redist.glfw.nuspec", "runtimes/linux-x64/native/libglfw.so.3.3", "runtimes/osx-x64/native/libglfw.3.dylib", "runtimes/win-x64/native/glfw3.dll", "runtimes/win-x86/native/glfw3.dll"]}, "OpenTK.Windowing.Common/4.3.0": {"sha512": "HKKMHsv+eQrB3C3ySCcYS0yfKNZ/SM0v1upqBLEN5khs7IVPQ/wRwdR+OaPH/9v11pYtdNQL/XtkPVV0SC8jWA==", "type": "package", "path": "opentk.windowing.common/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.1/OpenTK.Windowing.Common.dll", "lib/netstandard2.1/OpenTK.Windowing.Common.pdb", "lib/netstandard2.1/OpenTK.Windowing.Common.xml", "opentk.windowing.common.4.3.0.nupkg.sha512", "opentk.windowing.common.nuspec"]}, "OpenTK.Windowing.Desktop/4.3.0": {"sha512": "pomhIDKQGGLn+5jAWCUsxAcHLOwO4/43pmcXA2xVKRcrvRQgY0QCUcroQRDwGam+8SinRBSTa0PNVNbDxZhoRA==", "type": "package", "path": "opentk.windowing.desktop/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/OpenTK.Windowing.Desktop.dll", "lib/netcoreapp3.1/OpenTK.Windowing.Desktop.pdb", "lib/netcoreapp3.1/OpenTK.Windowing.Desktop.xml", "opentk.windowing.desktop.4.3.0.nupkg.sha512", "opentk.windowing.desktop.nuspec"]}, "OpenTK.Windowing.GraphicsLibraryFramework/4.3.0": {"sha512": "R8H4ynmYUYuow/QNx4pnvPGR8Z6KUP54rVlnsH5tRkDYBU/m6bWmvFFvAA492rRyM5nt+6zAqGjv8GXY+8sylA==", "type": "package", "path": "opentk.windowing.graphicslibraryframework/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/OpenTK.Windowing.GraphicsLibraryFramework.dll", "lib/netcoreapp3.1/OpenTK.Windowing.GraphicsLibraryFramework.pdb", "lib/netcoreapp3.1/OpenTK.Windowing.GraphicsLibraryFramework.xml", "opentk.windowing.graphicslibraryframework.4.3.0.nupkg.sha512", "opentk.windowing.graphicslibraryframework.nuspec"]}, "ScottPlot/5.0.55": {"sha512": "afhqrtF/eZ08QWunp56tDGg8gKiSAnNfUaB9XTAp3xt7QlVPhEkNmJKImTDC+/j6EbWENiezHaOKt9bD/kLm0w==", "type": "package", "path": "scottplot/5.0.55", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net462/ScottPlot.dll", "lib/net462/ScottPlot.xml", "lib/net6.0/ScottPlot.dll", "lib/net6.0/ScottPlot.xml", "lib/net8.0/ScottPlot.dll", "lib/net8.0/ScottPlot.xml", "lib/netstandard2.0/ScottPlot.dll", "lib/netstandard2.0/ScottPlot.xml", "readme.md", "scottplot.5.0.55.nupkg.sha512", "scottplot.nuspec"]}, "ScottPlot.WPF/5.0.55": {"sha512": "C1DC2XvRK5y9Ra9KWqGdwitiDuVA10lRwq6SSz8gC5B77FsKFukwrnIB8/GlDSTJUaUG17QMJWVty9e7xmI2og==", "type": "package", "path": "scottplot.wpf/5.0.55", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net462/ScottPlot.WPF.dll", "lib/net462/ScottPlot.WPF.xml", "lib/net6.0-windows7.0/ScottPlot.WPF.dll", "lib/net6.0-windows7.0/ScottPlot.WPF.xml", "readme.md", "scottplot.wpf.5.0.55.nupkg.sha512", "scottplot.wpf.nuspec"]}, "SkiaSharp/2.88.9": {"sha512": "3MD5VHjXXieSHCleRLuaTXmL2pD0mB7CcOB1x2kA1I4bhptf4e3R27iM93264ZYuAq6mkUyX5XbcxnZvMJYc1Q==", "type": "package", "path": "skiasharp/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/monoandroid1.0/SkiaSharp.dll", "lib/monoandroid1.0/SkiaSharp.pdb", "lib/monoandroid1.0/SkiaSharp.xml", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net462/SkiaSharp.xml", "lib/net6.0-android30.0/SkiaSharp.dll", "lib/net6.0-android30.0/SkiaSharp.pdb", "lib/net6.0-android30.0/SkiaSharp.xml", "lib/net6.0-ios13.6/SkiaSharp.dll", "lib/net6.0-ios13.6/SkiaSharp.pdb", "lib/net6.0-ios13.6/SkiaSharp.xml", "lib/net6.0-maccatalyst13.5/SkiaSharp.dll", "lib/net6.0-maccatalyst13.5/SkiaSharp.pdb", "lib/net6.0-maccatalyst13.5/SkiaSharp.xml", "lib/net6.0-macos10.15/SkiaSharp.dll", "lib/net6.0-macos10.15/SkiaSharp.pdb", "lib/net6.0-macos10.15/SkiaSharp.xml", "lib/net6.0-tizen7.0/SkiaSharp.dll", "lib/net6.0-tizen7.0/SkiaSharp.pdb", "lib/net6.0-tizen7.0/SkiaSharp.xml", "lib/net6.0-tvos13.4/SkiaSharp.dll", "lib/net6.0-tvos13.4/SkiaSharp.pdb", "lib/net6.0-tvos13.4/SkiaSharp.xml", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.xml", "lib/netcoreapp3.1/SkiaSharp.dll", "lib/netcoreapp3.1/SkiaSharp.pdb", "lib/netcoreapp3.1/SkiaSharp.xml", "lib/netstandard1.3/SkiaSharp.dll", "lib/netstandard1.3/SkiaSharp.pdb", "lib/netstandard1.3/SkiaSharp.xml", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.xml", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.xml", "lib/tizen40/SkiaSharp.dll", "lib/tizen40/SkiaSharp.pdb", "lib/tizen40/SkiaSharp.xml", "lib/uap10.0.10240/SkiaSharp.dll", "lib/uap10.0.10240/SkiaSharp.pdb", "lib/uap10.0.10240/SkiaSharp.xml", "lib/uap10.0.16299/SkiaSharp.dll", "lib/uap10.0.16299/SkiaSharp.pdb", "lib/uap10.0.16299/SkiaSharp.xml", "lib/xamarinios1.0/SkiaSharp.dll", "lib/xamarinios1.0/SkiaSharp.pdb", "lib/xamarinios1.0/SkiaSharp.xml", "lib/xamarinmac2.0/SkiaSharp.dll", "lib/xamarinmac2.0/SkiaSharp.pdb", "lib/xamarinmac2.0/SkiaSharp.xml", "lib/xamarintvos1.0/SkiaSharp.dll", "lib/xamarintvos1.0/SkiaSharp.pdb", "lib/xamarintvos1.0/SkiaSharp.xml", "lib/xamarinwatchos1.0/SkiaSharp.dll", "lib/xamarinwatchos1.0/SkiaSharp.pdb", "lib/xamarinwatchos1.0/SkiaSharp.xml", "skiasharp.2.88.9.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.HarfBuzz/2.88.9": {"sha512": "5lyjX04CSWTKFYMSNN6SMupV2qzYDr8W/u8S3ZEkr+Sg3kOi0YE06EOqG0tzn/YPBID89xam4L3rz3hUUKOGEQ==", "type": "package", "path": "skiasharp.harfbuzz/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net462/SkiaSharp.HarfBuzz.dll", "lib/net462/SkiaSharp.HarfBuzz.pdb", "lib/net462/SkiaSharp.HarfBuzz.xml", "lib/net6.0/SkiaSharp.HarfBuzz.dll", "lib/net6.0/SkiaSharp.HarfBuzz.pdb", "lib/net6.0/SkiaSharp.HarfBuzz.xml", "lib/netcoreapp3.1/SkiaSharp.HarfBuzz.dll", "lib/netcoreapp3.1/SkiaSharp.HarfBuzz.pdb", "lib/netcoreapp3.1/SkiaSharp.HarfBuzz.xml", "lib/netstandard1.3/SkiaSharp.HarfBuzz.dll", "lib/netstandard1.3/SkiaSharp.HarfBuzz.pdb", "lib/netstandard1.3/SkiaSharp.HarfBuzz.xml", "lib/netstandard2.0/SkiaSharp.HarfBuzz.dll", "lib/netstandard2.0/SkiaSharp.HarfBuzz.pdb", "lib/netstandard2.0/SkiaSharp.HarfBuzz.xml", "lib/netstandard2.1/SkiaSharp.HarfBuzz.dll", "lib/netstandard2.1/SkiaSharp.HarfBuzz.pdb", "lib/netstandard2.1/SkiaSharp.HarfBuzz.xml", "skiasharp.harfbuzz.2.88.9.nupkg.sha512", "skiasharp.harfbuzz.nuspec"]}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.9": {"sha512": "ZA9syMvIuoqxPhyFTgde/bNV6VMdeYT9gRdNQT5O+ZjPK+a/nVkKRrqBbn7EVA7XQiW+xIycyT4gW/sea9eh+w==", "type": "package", "path": "skiasharp.nativeassets.linux.nodependencies/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Linux.NoDependencies.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Linux.NoDependencies.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/linux-arm/native/libSkiaSharp.so", "runtimes/linux-arm64/native/libSkiaSharp.so", "runtimes/linux-musl-x64/native/libSkiaSharp.so", "runtimes/linux-x64/native/libSkiaSharp.so", "skiasharp.nativeassets.linux.nodependencies.2.88.9.nupkg.sha512", "skiasharp.nativeassets.linux.nodependencies.nuspec"]}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"sha512": "Nv5spmKc4505Ep7oUoJ5vp3KweFpeNqxpyGDWyeEPTX2uR6S6syXIm3gj75dM0YJz7NPvcix48mR5laqs8dPuA==", "type": "package", "path": "skiasharp.nativeassets.macos/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.2.88.9.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"sha512": "wb2kYgU7iy84nQLYZwMeJXixvK++GoIuECjU4ECaUKNuflyRlJKyiRhN1MAHswvlvzuvkrjRWlK0Za6+kYQK7w==", "type": "package", "path": "skiasharp.nativeassets.win32/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.2.88.9.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "SkiaSharp.Views.Desktop.Common/2.88.9": {"sha512": "a9geU/Xe5Tn6OnadnfnMCUqqO3vB7GwCxJM8f7rRay8JrGXm7rgYKBPQX0baymxmVlPqYXeMm8AWhQj5RVtwPw==", "type": "package", "path": "skiasharp.views.desktop.common/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net462/SkiaSharp.Views.Desktop.Common.dll", "lib/net462/SkiaSharp.Views.Desktop.Common.pdb", "lib/net462/SkiaSharp.Views.Desktop.Common.xml", "lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.dll", "lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.pdb", "lib/netcoreapp3.1/SkiaSharp.Views.Desktop.Common.xml", "lib/netstandard2.0/SkiaSharp.Views.Desktop.Common.dll", "lib/netstandard2.0/SkiaSharp.Views.Desktop.Common.pdb", "lib/netstandard2.0/SkiaSharp.Views.Desktop.Common.xml", "skiasharp.views.desktop.common.2.88.9.nupkg.sha512", "skiasharp.views.desktop.common.nuspec"]}, "SkiaSharp.Views.WPF/2.88.9": {"sha512": "80fEK0dYEJpi36Ko9WylN2rCf4w/hzzHEe+nyEL79dTHvUVfuXLZONmIwrDJ+pL5JSi8YmktQVxbYZexS+NZCw==", "type": "package", "path": "skiasharp.views.wpf/2.88.9", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net462/SkiaSharp.Views.WPF.dll", "lib/net462/SkiaSharp.Views.WPF.pdb", "lib/net462/SkiaSharp.Views.WPF.xml", "lib/netcoreapp3.1/SkiaSharp.Views.WPF.dll", "lib/netcoreapp3.1/SkiaSharp.Views.WPF.pdb", "lib/netcoreapp3.1/SkiaSharp.Views.WPF.xml", "skiasharp.views.wpf.2.88.9.nupkg.sha512", "skiasharp.views.wpf.nuspec"]}, "System.Drawing.Common/4.7.3": {"sha512": "B3+wwhAeoUQ6KeshWSq3IRLQiMoqPEzSHzyVyxTI/EbYuqIp90lXrRISlip2AF+5tj74ycIVPpnRY4M424HahA==", "type": "package", "path": "system.drawing.common/4.7.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.Drawing.Common.dll", "ref/netcoreapp3.0/System.Drawing.Common.dll", "ref/netcoreapp3.0/System.Drawing.Common.xml", "ref/netstandard2.0/System.Drawing.Common.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.0/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.xml", "runtimes/win/lib/netcoreapp2.0/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.xml", "system.drawing.common.4.7.3.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["Newtonsoft.Json >= 13.0.3", "ScottPlot.WPF >= 5.0.55"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\TEST\\CryptoDeviationViewer\\CryptoDeviationViewer.csproj", "projectName": "CryptoDeviationViewer", "projectPath": "E:\\TEST\\CryptoDeviationViewer\\CryptoDeviationViewer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\TEST\\CryptoDeviationViewer\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "ScottPlot.WPF": {"target": "Package", "version": "[5.0.55, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}