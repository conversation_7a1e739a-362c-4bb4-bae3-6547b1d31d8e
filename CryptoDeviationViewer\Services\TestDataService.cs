using CryptoDeviationViewer.Models;

namespace CryptoDeviationViewer.Services
{
    /// <summary>
    /// 测试数据生成服务，用于验证动态Y轴缩放功能
    /// </summary>
    public class TestDataService
    {
        private readonly Random _random = new Random();
        private int _testCycle = 0;

        // 扩展到300个测试symbol
        private readonly string[] _testSymbols = {
            // 主流币种 (20个)
            "BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "XRPUSDT",
            "SOLUSDT", "DOTUSDT", "DOGEUSDT", "AVAXUSDT", "SHIBUSDT",
            "MATICUSDT", "LTCUSDT", "ATOMUSDT", "LINKUSDT", "UNIUSDT",
            "BCHUSDT", "XLMUSDT", "ALGOUSDT", "VETUSDT", "FILUSDT",

            // DeFi币种 (30个)
            "AAVEUSDT", "COMPUSDT", "MKRUSDT", "YFIUSDT", "SUSHIUSDT",
            "CAKEUSDT", "1INCHUSDT", "CRVUSDT", "BALUSDT", "SNXUSDT",
            "ALPHAUSDT", "BANDUSDT", "KNCUSDT", "LRCUSDT", "RENUSDT",
            "ZRXUSDT", "STORJUSDT", "OCEANUSDT", "INJUSDT", "DYDXUSDT",
            "GMXUSDT", "LDOUSDT", "RDNTUSDT", "MAGICUSDT", "ARBUSDT",
            "OPUSDT", "APTUSDT", "SUIUSDT", "BLURUSDT", "PEPEUSDT",

            // Layer1/Layer2 (25个)
            "NEARUSDT", "FTMUSDT", "ONEUSDT", "ZILUSDT", "ICXUSDT",
            "ONTUSDT", "QTUMUSDT", "WAVESUSDT", "LSKUSDT", "ARKUSDT",
            "STXUSDT", "EGLDUSDT", "FLOWUSDT", "ICPUSDT", "THETAUSDT",
            "TFUELUSDT", "ENJUSDT", "MANAUSDT", "SANDUSDT", "AXSUSDT",
            "GALAUSDT", "CHZUSDT", "AUDIOUSDT", "MASKUSDT", "ROSEUSDT",

            // 小市值币种 (225个) - 生成更多测试数据
        };

        // 动态生成更多测试symbol
        private readonly List<string> _allTestSymbols;

        public TestDataService()
        {
            // 初始化完整的300个测试symbol列表
            _allTestSymbols = new List<string>(_testSymbols);

            // 生成额外的小市值币种到300个
            var additionalSymbols = new List<string>();
            for (int i = _testSymbols.Length; i < 300; i++)
            {
                additionalSymbols.Add($"TEST{i:D3}USDT"); // TEST001USDT, TEST002USDT, etc.
            }
            _allTestSymbols.AddRange(additionalSymbols);
        }

        /// <summary>
        /// 生成测试市场分析数据
        /// </summary>
        /// <returns>模拟的市场分析结果</returns>
        public MarketAnalysis GenerateTestMarketAnalysis()
        {
            _testCycle++;
            var timestamp = DateTime.Now;
            var deviationPoints = new List<DeviationPoint>();

            // 根据测试周期生成不同类型的数据
            var testScenario = GetTestScenario(_testCycle);

            foreach (var symbol in _allTestSymbols)
            {
                var deviation = GenerateDeviationForScenario(testScenario, symbol);
                var basePrice = 100.0; // 模拟基础价格
                var indexPrice = basePrice;
                var actualPrice = basePrice * (1 + deviation);

                deviationPoints.Add(new DeviationPoint
                {
                    Symbol = symbol,
                    Timestamp = timestamp,
                    Deviation = deviation,
                    Price = actualPrice,
                    IndexPrice = indexPrice
                });
            }

            return new MarketAnalysis
            {
                Timestamp = timestamp,
                DeviationPoints = deviationPoints,
                TotalSymbols = deviationPoints.Count,
                AverageDeviation = deviationPoints.Average(p => Math.Abs(p.Deviation)),
                PositiveDeviations = deviationPoints.Count(p => p.Deviation > 0),
                NegativeDeviations = deviationPoints.Count(p => p.Deviation < 0)
            };
        }

        /// <summary>
        /// 根据测试周期确定测试场景
        /// </summary>
        private TestScenario GetTestScenario(int cycle)
        {
            var scenarioIndex = (cycle / 8) % 10; // 每8秒切换一次场景，增加到10种场景
            return (TestScenario)scenarioIndex;
        }

        /// <summary>
        /// 根据测试场景生成偏差值
        /// </summary>
        private double GenerateDeviationForScenario(TestScenario scenario, string symbol)
        {
            var isMainCoin = IsMainCoin(symbol);
            var isDeFiCoin = IsDeFiCoin(symbol);
            var isSmallCap = IsSmallCapCoin(symbol);

            switch (scenario)
            {
                case TestScenario.Normal:
                    // 正常市场：99%数据在±0.5%范围内
                    return (_random.NextDouble() - 0.5) * 0.01; // ±0.5%

                case TestScenario.MildVolatility:
                    // 轻微波动：大部分在±1%，少数在±3%
                    if (_random.NextDouble() < 0.85)
                        return (_random.NextDouble() - 0.5) * 0.02; // ±1%
                    else
                        return (_random.NextDouble() - 0.5) * 0.06; // ±3%

                case TestScenario.ModerateExtreme:
                    // 中等极端：部分symbol出现±5-8%偏差
                    if (_random.NextDouble() < 0.7)
                        return (_random.NextDouble() - 0.5) * 0.02; // ±1%
                    else if (_random.NextDouble() < 0.9)
                        return (_random.NextDouble() - 0.5) * 0.10; // ±5%
                    else
                        return (_random.NextDouble() - 0.5) * 0.16; // ±8%

                case TestScenario.HighExtreme:
                    // 高度极端：多个symbol出现±10-15%偏差
                    if (_random.NextDouble() < 0.5)
                        return (_random.NextDouble() - 0.5) * 0.04; // ±2%
                    else if (_random.NextDouble() < 0.8)
                        return (_random.NextDouble() - 0.5) * 0.20; // ±10%
                    else
                        return (_random.NextDouble() - 0.5) * 0.30; // ±15%

                case TestScenario.ExtremeVolatility:
                    // 极端波动：模拟市场崩盘或暴涨
                    if (isMainCoin)
                        return (_random.NextDouble() - 0.5) * 0.40; // ±20% (主要币种)
                    else if (_random.NextDouble() < 0.6)
                        return (_random.NextDouble() - 0.5) * 0.30; // ±15%
                    else
                        return (_random.NextDouble() - 0.5) * 0.10; // ±5%

                case TestScenario.AsymmetricCrash:
                    // 非对称崩盘：大部分负偏差，少数正偏差
                    if (_random.NextDouble() < 0.8)
                        return -Math.Abs((_random.NextDouble() - 0.5) * 0.25); // 负偏差 -12.5%
                    else
                        return Math.Abs((_random.NextDouble() - 0.5) * 0.10); // 正偏差 +5%

                case TestScenario.AsymmetricPump:
                    // 非对称暴涨：大部分正偏差，少数负偏差
                    if (_random.NextDouble() < 0.8)
                        return Math.Abs((_random.NextDouble() - 0.5) * 0.25); // 正偏差 +12.5%
                    else
                        return -Math.Abs((_random.NextDouble() - 0.5) * 0.10); // 负偏差 -5%

                case TestScenario.SectorRotation:
                    // 板块轮动：DeFi币种暴涨，主流币种下跌
                    if (isDeFiCoin)
                        return Math.Abs((_random.NextDouble() - 0.5) * 0.30); // DeFi +15%
                    else if (isMainCoin)
                        return -Math.Abs((_random.NextDouble() - 0.5) * 0.20); // 主流 -10%
                    else
                        return (_random.NextDouble() - 0.5) * 0.06; // 其他 ±3%

                case TestScenario.SmallCapMania:
                    // 小市值狂热：小币种极端波动
                    if (isSmallCap)
                        return (_random.NextDouble() - 0.5) * 0.60; // 小币 ±30%
                    else if (isMainCoin)
                        return (_random.NextDouble() - 0.5) * 0.08; // 主流 ±4%
                    else
                        return (_random.NextDouble() - 0.5) * 0.16; // DeFi ±8%

                case TestScenario.FlashCrash:
                    // 闪崩：随机选择20%的币种出现极端负偏差
                    if (_random.NextDouble() < 0.2) // 20%概率
                        return -Math.Abs((_random.NextDouble() - 0.5) * 0.50); // 极端负偏差 -25%
                    else if (_random.NextDouble() < 0.1) // 10%概率反弹
                        return Math.Abs((_random.NextDouble() - 0.5) * 0.20); // 反弹 +10%
                    else
                        return (_random.NextDouble() - 0.5) * 0.04; // 正常 ±2%

                default:
                    return 0;
            }
        }

        /// <summary>
        /// 判断是否为主流币种
        /// </summary>
        private bool IsMainCoin(string symbol)
        {
            var mainCoins = new[] { "BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "XRPUSDT",
                                   "SOLUSDT", "DOTUSDT", "AVAXUSDT", "MATICUSDT", "LTCUSDT" };
            return mainCoins.Contains(symbol);
        }

        /// <summary>
        /// 判断是否为DeFi币种
        /// </summary>
        private bool IsDeFiCoin(string symbol)
        {
            var defiCoins = new[] { "AAVEUSDT", "COMPUSDT", "MKRUSDT", "YFIUSDT", "SUSHIUSDT",
                                   "CAKEUSDT", "1INCHUSDT", "CRVUSDT", "UNIUSDT", "LINKUSDT" };
            return defiCoins.Contains(symbol);
        }

        /// <summary>
        /// 判断是否为小市值币种
        /// </summary>
        private bool IsSmallCapCoin(string symbol)
        {
            return symbol.StartsWith("TEST") ||
                   (!IsMainCoin(symbol) && !IsDeFiCoin(symbol));
        }

        /// <summary>
        /// 获取当前测试场景的描述
        /// </summary>
        public string GetCurrentScenarioDescription()
        {
            var scenario = GetTestScenario(_testCycle);
            return scenario switch
            {
                TestScenario.Normal => "正常市场 (±0.5%)",
                TestScenario.MildVolatility => "轻微波动 (±1-3%)",
                TestScenario.ModerateExtreme => "中等极端 (±5-8%)",
                TestScenario.HighExtreme => "高度极端 (±10-15%)",
                TestScenario.ExtremeVolatility => "极端波动 (±15-20%)",
                TestScenario.AsymmetricCrash => "非对称崩盘 (负偏差主导)",
                TestScenario.AsymmetricPump => "非对称暴涨 (正偏差主导)",
                TestScenario.SectorRotation => "板块轮动 (DeFi涨主流跌)",
                TestScenario.SmallCapMania => "小币狂热 (小市值±30%)",
                TestScenario.FlashCrash => "闪崩模式 (20%币种-25%)",
                _ => "未知场景"
            };
        }

        /// <summary>
        /// 重置测试周期
        /// </summary>
        public void ResetTestCycle()
        {
            _testCycle = 0;
        }
    }

    /// <summary>
    /// 测试场景枚举
    /// </summary>
    public enum TestScenario
    {
        Normal = 0,              // 正常市场
        MildVolatility = 1,      // 轻微波动
        ModerateExtreme = 2,     // 中等极端
        HighExtreme = 3,         // 高度极端
        ExtremeVolatility = 4,   // 极端波动
        AsymmetricCrash = 5,     // 非对称崩盘
        AsymmetricPump = 6,      // 非对称暴涨
        SectorRotation = 7,      // 板块轮动
        SmallCapMania = 8,       // 小市值狂热
        FlashCrash = 9           // 闪崩模式
    }
}
