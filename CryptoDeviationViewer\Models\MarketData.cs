using Newtonsoft.Json;

namespace CryptoDeviationViewer.Models
{
    /// <summary>
    /// Binance Premium Index API响应模型
    /// </summary>
    public class PremiumIndexData
    {
        [JsonProperty("symbol")]
        public string Symbol { get; set; } = string.Empty;

        [JsonProperty("markPrice")]
        public string MarkPrice { get; set; } = string.Empty;

        [JsonProperty("indexPrice")]
        public string IndexPrice { get; set; } = string.Empty;

        [JsonProperty("estimatedSettlePrice")]
        public string EstimatedSettlePrice { get; set; } = string.Empty;

        [JsonProperty("lastFundingRate")]
        public string LastFundingRate { get; set; } = string.Empty;

        [JsonProperty("interestRate")]
        public string InterestRate { get; set; } = string.Empty;

        [JsonProperty("nextFundingTime")]
        public long NextFundingTime { get; set; }

        [JsonProperty("time")]
        public long Time { get; set; }
    }

    /// <summary>
    /// Binance Price API响应模型
    /// </summary>
    public class PriceData
    {
        [JsonProperty("symbol")]
        public string Symbol { get; set; } = string.Empty;

        [JsonProperty("price")]
        public string Price { get; set; } = string.Empty;

        [JsonProperty("time")]
        public long Time { get; set; }
    }

    /// <summary>
    /// 偏差数据点模型
    /// </summary>
    public class DeviationPoint
    {
        /// <summary>
        /// 交易对符号
        /// </summary>
        public string Symbol { get; set; } = string.Empty;

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 价格偏差百分比 (price/indexPrice - 1)
        /// </summary>
        public double Deviation { get; set; }

        /// <summary>
        /// 当前价格
        /// </summary>
        public double Price { get; set; }

        /// <summary>
        /// 指数价格
        /// </summary>
        public double IndexPrice { get; set; }
    }

    /// <summary>
    /// 市场分析结果
    /// </summary>
    public class MarketAnalysis
    {
        /// <summary>
        /// 分析时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 总交易对数量
        /// </summary>
        public int TotalSymbols { get; set; }

        /// <summary>
        /// 平均偏差
        /// </summary>
        public double AverageDeviation { get; set; }

        /// <summary>
        /// 偏差标准差
        /// </summary>
        public double StandardDeviation { get; set; }

        /// <summary>
        /// 最大偏差
        /// </summary>
        public double MaxDeviation { get; set; }

        /// <summary>
        /// 最小偏差
        /// </summary>
        public double MinDeviation { get; set; }

        /// <summary>
        /// 正偏差数量
        /// </summary>
        public int PositiveDeviations { get; set; }

        /// <summary>
        /// 负偏差数量
        /// </summary>
        public int NegativeDeviations { get; set; }

        /// <summary>
        /// 偏差数据点列表
        /// </summary>
        public List<DeviationPoint> DeviationPoints { get; set; } = new List<DeviationPoint>();
    }
}
