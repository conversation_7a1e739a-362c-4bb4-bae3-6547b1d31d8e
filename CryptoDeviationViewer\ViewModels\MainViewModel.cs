using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Timers;
using System.Windows;
using CryptoDeviationViewer.Commands;
using CryptoDeviationViewer.Models;
using CryptoDeviationViewer.Services;
using ScottPlot;
using ScottPlot.Plottables;

namespace CryptoDeviationViewer.ViewModels
{
    /// <summary>
    /// 主窗口ViewModel
    /// </summary>
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly MarketDataService _marketDataService;
        private readonly TestDataService _testDataService;
        private readonly System.Timers.Timer _updateTimer;
        private readonly List<MarketAnalysis> _historyData;
        private const int MAX_HISTORY_POINTS = 30; // 2秒更新，保持60秒的历史数据，流动效果更明显
        private const int UPDATE_INTERVAL_SECONDS = 2; // 增加到2秒以减少并发冲突
        private const int MAX_SCATTER_POINTS = 2000; // 限制散点图最大数据点数量，提升渲染性能

        // ScottPlot相关
        private Plot? _plot;
        private ScottPlot.WPF.WpfPlot? _wpfPlot; // 添加WPF控件引用
        private readonly List<ScottPlot.Plottables.Scatter> _scatterPlots = new List<ScottPlot.Plottables.Scatter>();
        private ScottPlot.Plottables.HorizontalLine? _zeroLine;
        private readonly object _plotLock = new object(); // 线程同步锁
        private volatile bool _isUpdating = false; // 防止定时器重叠执行

        // 颜色映射配置
        private const double MAX_DEVIATION_PERCENT = 10.0; // ±10%
        private const int COLOR_LEVELS = 100; // 100档颜色

        // 动态Y轴缩放配置
        private const double NORMAL_Y_RANGE = 2.0; // 正常模式：±2%
        private const double EXTREME_THRESHOLD = 3.0; // 异常检测阈值：±3%
        private const double AUTO_EXPAND_THRESHOLD = 5.0; // 自动扩展阈值：±5%
        private const double MAX_AUTO_EXPAND_RANGE = 15.0; // 最大自动扩展范围：±15%

        // Y轴状态
        private double _currentYRange = NORMAL_Y_RANGE;
        private bool _isInExtremeMode = false;

        // 流动效果相关
        private const double FLOW_WINDOW_SIZE = 30.0; // 显示窗口大小（X轴单位）
        private const double NORMAL_NEW_DATA_X_POSITION = 29.0; // 普通模式新数据位置（右侧）
        private const double STREAMING_NEW_DATA_X_POSITION = 28.0; // 流式播放模式新数据位置（倒数第二个）

        // 用户缩放状态跟踪
        private bool _userHasZoomed = false;

        // 流式播放状态
        private bool _isStreamingMode = false;
        private double _userXMin = 0;
        private double _userXMax = 0;
        private double _userYMin = 0;
        private double _userYMax = 0;

        // 绑定属性
        private string _statusText = "准备就绪";
        private string _lastUpdateTime = "";
        private int _totalSymbols = 0;
        private double _averageDeviation = 0;
        private bool _isRunning = false;
        private string _yAxisMode = "正常模式 (±2%)";
        private int _extremeSymbolsCount = 0;
        private bool _isTestMode = false;
        private string _testScenarioDescription = "";

        public MainViewModel()
        {
            _marketDataService = new MarketDataService();
            _testDataService = new TestDataService();
            _historyData = new List<MarketAnalysis>();

            // 设置定时器
            _updateTimer = new System.Timers.Timer(UPDATE_INTERVAL_SECONDS * 1000);
            _updateTimer.Elapsed += OnTimerElapsed;
            _updateTimer.AutoReset = true;

            // 启动数据更新
            StartDataUpdate();
        }

        #region 绑定属性

        public string StatusText
        {
            get => _statusText;
            set => SetProperty(ref _statusText, value);
        }

        public string LastUpdateTime
        {
            get => _lastUpdateTime;
            set => SetProperty(ref _lastUpdateTime, value);
        }

        public int TotalSymbols
        {
            get => _totalSymbols;
            set => SetProperty(ref _totalSymbols, value);
        }

        public double AverageDeviation
        {
            get => _averageDeviation;
            set => SetProperty(ref _averageDeviation, value);
        }

        public bool IsRunning
        {
            get => _isRunning;
            set => SetProperty(ref _isRunning, value);
        }

        public string YAxisMode
        {
            get => _yAxisMode;
            set => SetProperty(ref _yAxisMode, value);
        }

        public int ExtremeSymbolsCount
        {
            get => _extremeSymbolsCount;
            set => SetProperty(ref _extremeSymbolsCount, value);
        }

        public bool IsTestMode
        {
            get => _isTestMode;
            set => SetProperty(ref _isTestMode, value);
        }

        public string TestScenarioDescription
        {
            get => _testScenarioDescription;
            set => SetProperty(ref _testScenarioDescription, value);
        }

        public bool IsStreamingMode
        {
            get => _isStreamingMode;
            set => SetProperty(ref _isStreamingMode, value);
        }

        #endregion

        #region 命令

        /// <summary>
        /// 切换测试模式命令
        /// </summary>
        public RelayCommand ToggleTestModeCommand => new RelayCommand(ToggleTestMode);

        /// <summary>
        /// 重置缩放命令
        /// </summary>
        public RelayCommand ResetZoomCommand => new RelayCommand(ResetZoom);

        /// <summary>
        /// 流式播放命令
        /// </summary>
        public RelayCommand StreamingCommand => new RelayCommand(ToggleStreamingMode);

        private void ToggleTestMode()
        {
            IsTestMode = !IsTestMode;

            if (IsTestMode)
            {
                _testDataService.ResetTestCycle();
                StatusText = "测试模式已启用 - 使用模拟数据";
            }
            else
            {
                StatusText = "测试模式已关闭 - 使用实时数据";
            }
        }

        private void ResetZoom()
        {
            if (_plot == null) return;

            try
            {
                // 重置用户缩放状态
                _userHasZoomed = false;

                // 重新计算并设置默认的X轴范围 - 流动效果
                if (_historyData.Count > 0)
                {
                    var minX = 0.0;
                    var maxX = FLOW_WINDOW_SIZE;
                    _plot.Axes.SetLimitsX(minX, maxX);
                }

                // 重新设置Y轴范围
                _plot.Axes.SetLimitsY(-_currentYRange, _currentYRange);

                // 刷新图表
                RefreshPlot();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"重置缩放时发生错误: {ex.Message}");
            }
        }

        private void ToggleStreamingMode()
        {
            IsStreamingMode = !IsStreamingMode;

            if (IsStreamingMode)
            {
                StatusText = "流式播放模式已启用 - 新数据将在倒数第二个位置出现";
            }
            else
            {
                StatusText = "流式播放模式已关闭 - 恢复正常显示模式";
            }

            // 重新绘制图表以应用新的显示模式
            if (_plot != null && _historyData.Count > 0)
            {
                UpdatePlot();
                RefreshPlot();
            }
        }

        /// <summary>
        /// 检查用户是否手动缩放了图表
        /// </summary>
        private void CheckUserZoom()
        {
            if (_plot == null) return;

            try
            {
                // 获取当前轴限制
                var currentXLimits = _plot.Axes.GetLimits();

                // 计算默认的X轴范围 - 流动效果
                var defaultMinX = 0.0;
                var defaultMaxX = FLOW_WINDOW_SIZE;

                // 检查用户是否手动缩放了X轴（允许小的误差）
                var xRangeChanged = Math.Abs(currentXLimits.Left - defaultMinX) > 0.1 ||
                                   Math.Abs(currentXLimits.Right - defaultMaxX) > 0.1;

                // 检查用户是否手动缩放了Y轴
                var yRangeChanged = Math.Abs(currentXLimits.Bottom - (-_currentYRange)) > 0.1 ||
                                   Math.Abs(currentXLimits.Top - _currentYRange) > 0.1;

                if (xRangeChanged || yRangeChanged)
                {
                    // 用户进行了手动缩放
                    _userHasZoomed = true;
                    _userXMin = currentXLimits.Left;
                    _userXMax = currentXLimits.Right;
                    _userYMin = currentXLimits.Bottom;
                    _userYMax = currentXLimits.Top;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查用户缩放时发生错误: {ex.Message}");
            }
        }

        #endregion

        /// <summary>
        /// 初始化ScottPlot图表
        /// </summary>
        public void InitializePlot(Plot plot, ScottPlot.WPF.WpfPlot? wpfPlot = null)
        {
            try
            {
                _plot = plot;
                _wpfPlot = wpfPlot; // 保存WPF控件引用

                // 清除现有内容
                _plot.Clear();

                // 设置图表基本属性
                _plot.Title("加密货币价格偏差分布");
                _plot.XLabel("时间 (UTC)");
                _plot.YLabel("偏差百分比 (%)");

                // 禁用图例以避免并发修改异常
                _plot.Legend.IsVisible = false;

                // 设置中文字体支持
                try
                {
                    // 尝试设置支持中文的字体
                    _plot.Font.Set("Microsoft YaHei");
                }
                catch
                {
                    // 如果字体设置失败，使用默认字体
                    Console.WriteLine("中文字体设置失败，使用默认字体");
                }

                // 设置Y轴范围（初始为正常模式）
                _plot.Axes.SetLimitsY(-_currentYRange, _currentYRange);

                // 设置暗黑主题
                _plot.FigureBackground.Color = Color.FromHex("#1E1E1E");  // 图表外部背景
                _plot.DataBackground.Color = Color.FromHex("#1E1E1E");    // 数据区域背景

                // 设置坐标轴和网格颜色
                _plot.Axes.Color(Color.FromHex("#CCCCCC"));               // 坐标轴颜色
                _plot.Grid.MajorLineColor = Color.FromHex("#333333");     // 主网格线颜色
                _plot.Grid.MinorLineColor = Color.FromHex("#222222");     // 次网格线颜色

                // 添加零线并保存引用
                _zeroLine = _plot.Add.HorizontalLine(0);
                _zeroLine.Color = Color.FromHex("#FCD535"); // 黄色零线
                _zeroLine.LineWidth = 2; // 2像素粗细
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化图表时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动数据更新
        /// </summary>
        private void StartDataUpdate()
        {
            IsRunning = true;
            StatusText = "正在获取数据...";
            _updateTimer.Start();
            
            // 立即执行一次更新
            _ = Task.Run(UpdateDataAsync);
        }

        /// <summary>
        /// 停止数据更新
        /// </summary>
        public void StopDataUpdate()
        {
            IsRunning = false;
            _updateTimer.Stop();
            StatusText = "已停止";
        }

        /// <summary>
        /// 定时器事件处理
        /// </summary>
        private async void OnTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            // 防止重叠执行
            if (_isUpdating)
            {
                Console.WriteLine("上次更新尚未完成，跳过本次更新");
                return;
            }

            await UpdateDataAsync();
        }

        /// <summary>
        /// 更新数据
        /// </summary>
        private async Task UpdateDataAsync()
        {
            // 设置更新标志，防止重叠执行
            if (_isUpdating)
            {
                return;
            }

            _isUpdating = true;

            // 暂停定时器以避免并发更新
            var wasTimerEnabled = _updateTimer.Enabled;
            if (wasTimerEnabled)
            {
                _updateTimer.Stop();
            }

            try
            {
                MarketAnalysis? analysis;

                if (IsTestMode)
                {
                    // 使用测试数据
                    analysis = _testDataService.GenerateTestMarketAnalysis();
                    TestScenarioDescription = _testDataService.GetCurrentScenarioDescription();
                }
                else
                {
                    // 使用实时数据
                    analysis = await _marketDataService.GetMarketAnalysisAsync();
                    TestScenarioDescription = "";
                }

                if (analysis == null)
                {
                    StatusText = IsTestMode ? "测试数据生成失败" : "数据获取失败";
                    return;
                }

                // 更新历史数据
                var wasDataRemoved = _historyData.Count >= MAX_HISTORY_POINTS;
                _historyData.Add(analysis);

                if (_historyData.Count > MAX_HISTORY_POINTS)
                {
                    _historyData.RemoveAt(0);
                    // 当移除旧数据时，需要移除对应的散点图
                    RemoveOldestScatterPlot();
                }

                // 更新UI属性
                Application.Current.Dispatcher.Invoke(() =>
                {
                    try
                    {
                        TotalSymbols = analysis.TotalSymbols;
                        AverageDeviation = analysis.AverageDeviation * 100; // 转换为百分比
                        LastUpdateTime = analysis.Timestamp.ToString("HH:mm:ss");

                        // 动态调整Y轴范围
                        UpdateYAxisRange(analysis);

                        // 增强状态信息显示
                        var positiveCount = analysis.PositiveDeviations;
                        var negativeCount = analysis.NegativeDeviations;
                        var modePrefix = IsTestMode ? "[测试模式] " : "";
                        StatusText = $"{modePrefix}运行中 - {analysis.DeviationPoints.Count} 个交易对 (正偏差: {positiveCount}, 负偏差: {negativeCount})";

                        // 检查用户是否手动缩放（流式播放模式下保持当前缩放）
                        if (!IsStreamingMode)
                        {
                            CheckUserZoom();
                        }

                        // 更新图表并同步刷新（在同一个锁保护下）
                        UpdatePlotAndRefresh();
                    }
                    catch (Exception uiEx)
                    {
                        Console.WriteLine($"UI更新时发生错误: {uiEx.Message}");
                        StatusText = $"UI更新错误: {uiEx.Message}";
                    }
                });
            }
            catch (Exception ex)
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    StatusText = $"错误: {ex.Message}";
                });
                Console.WriteLine($"数据更新时发生错误: {ex.Message}");
            }
            finally
            {
                // 重置更新标志
                _isUpdating = false;

                // 恢复定时器
                if (wasTimerEnabled && IsRunning)
                {
                    _updateTimer.Start();
                }
            }
        }

        /// <summary>
        /// 更新散点图 - 完全重绘策略（避免并发问题）
        /// </summary>
        private void UpdatePlot()
        {
            if (_plot == null || _historyData.Count == 0)
                return;

            try
            {
                lock (_plotLock)
                {
                    // 检查Plot状态
                    if (_plot.PlottableList == null)
                    {
                        Console.WriteLine("Plot状态异常，跳过更新");
                        return;
                    }

                    // 简化策略：始终完全重绘以避免并发问题
                    RedrawAllPlots();
                    // 更新X轴标签和范围
                    UpdateAxisLabels();
                }
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"更新图表时发生并发修改异常: {ex.Message}");
                // 记录错误但不重试，避免递归调用
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新图表时发生错误: {ex.Message}");
                try
                {
                    // 发生错误时重新初始化图表
                    lock (_plotLock)
                    {
                        _scatterPlots.Clear();
                        if (_plot != null)
                        {
                            _plot.Clear();
                        }
                    }
                    if (_plot != null)
                    {
                        InitializePlot(_plot, _wpfPlot);
                    }
                }
                catch (Exception initEx)
                {
                    Console.WriteLine($"重新初始化图表时发生错误: {initEx.Message}");
                }
            }
        }

        /// <summary>
        /// 更新图表并同步刷新（线程安全版本）
        /// </summary>
        private void UpdatePlotAndRefresh()
        {
            if (_plot == null || _historyData.Count == 0)
                return;

            try
            {
                lock (_plotLock)
                {
                    // 检查Plot状态
                    if (_plot.PlottableList == null)
                    {
                        Console.WriteLine("Plot状态异常，跳过更新");
                        return;
                    }

                    // 完全重绘以避免并发问题
                    RedrawAllPlots();
                    // 更新X轴标签和范围
                    UpdateAxisLabels();

                    // 在同一个锁保护下同步刷新
                    if (_wpfPlot != null)
                    {
                        _wpfPlot.Refresh();
                    }
                }
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"更新图表时发生并发修改异常: {ex.Message}");
                // 记录错误但不重试，避免递归调用
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新图表时发生错误: {ex.Message}");
                try
                {
                    // 发生错误时重新初始化图表
                    lock (_plotLock)
                    {
                        _scatterPlots.Clear();
                        if (_plot != null)
                        {
                            _plot.Clear();
                        }
                    }
                    if (_plot != null)
                    {
                        InitializePlot(_plot, _wpfPlot);
                    }
                }
                catch (Exception initEx)
                {
                    Console.WriteLine($"重新初始化图表时发生错误: {initEx.Message}");
                }
            }
        }

        /// <summary>
        /// 添加新的散点图（流水式更新）
        /// </summary>
        private void AddNewScatterPlot()
        {
            if (_plot == null || _historyData.Count == 0)
                return;

            lock (_plotLock)
            {
                // 获取最新的数据点
                var latestAnalysis = _historyData.Last();
                var dataIndex = _historyData.Count - 1;

                // 计算新数据点的X坐标
                var baseX = dataIndex * 1.0;
                var random = new Random(latestAnalysis.Timestamp.GetHashCode());

                // 按偏差值分组，为每个偏差值创建单独的散点图以实现不同颜色
                var deviationGroups = latestAnalysis.DeviationPoints
                    .GroupBy(p => Math.Round(p.Deviation * 100, 2)) // 按偏差百分比分组，保留2位小数
                    .ToList();

            foreach (var group in deviationGroups)
            {
                var xValues = new List<double>();
                var yValues = new List<double>();
                var deviationPercent = group.Key;

                foreach (var point in group)
                {
                    // 在基础X值上添加小范围随机偏移 (-0.4 到 +0.4)
                    var xOffset = (random.NextDouble() - 0.5) * 0.8;
                    xValues.Add(baseX + xOffset);
                    yValues.Add(point.Deviation * 100); // 转换为百分比
                }

                if (xValues.Count > 0)
                {
                    // 性能优化：限制单个散点图的数据点数量
                    double[] finalXValues, finalYValues;
                    var maxPointsPerScatter = MAX_SCATTER_POINTS / Math.Max(1, _historyData.Count * deviationGroups.Count);

                    if (xValues.Count > maxPointsPerScatter)
                    {
                        var step = Math.Max(1, xValues.Count / maxPointsPerScatter);
                        finalXValues = xValues.Where((x, i) => i % step == 0).ToArray();
                        finalYValues = yValues.Where((y, i) => i % step == 0).ToArray();
                    }
                    else
                    {
                        finalXValues = xValues.ToArray();
                        finalYValues = yValues.ToArray();
                    }

                    var scatter = _plot.Add.Scatter(finalXValues, finalYValues);

                    // 根据偏差值设置颜色
                    var baseColor = GetDeviationColor(deviationPercent);

                    // 根据数据新旧程度设置透明度（新数据更明显）
                    var alpha = Math.Max(0.3, 0.9 - (dataIndex * 0.05)); // 更平滑的透明度过渡
                    scatter.Color = baseColor.WithAlpha(alpha);
                    scatter.MarkerSize = 6;
                    scatter.LineWidth = 0;
                    scatter.MarkerShape = MarkerShape.FilledCircle;

                    // 保存散点图引用
                    _scatterPlots.Add(scatter);
                }
            }
            }
        }

        /// <summary>
        /// 完全重绘所有散点图（调用前需要已获得锁）
        /// </summary>
        private void RedrawAllPlots()
        {
            if (_plot == null)
                return;

            try
            {
                // 清除所有散点图
                foreach (var scatter in _scatterPlots)
                {
                    _plot.Remove(scatter);
                }
                _scatterPlots.Clear();

                // 重新添加零线（如果被清除了）
                if (_zeroLine == null || !_plot.PlottableList.Contains(_zeroLine))
                {
                    _zeroLine = _plot.Add.HorizontalLine(0);
                    _zeroLine.Color = Color.FromHex("#FCD535"); // 黄色零线
                    _zeroLine.LineWidth = 2; // 2像素粗细
                }

                // 重新绘制所有历史数据 - 实现流动效果
                for (int i = 0; i < _historyData.Count; i++)
                {
                    var analysis = _historyData[i];
                    // 根据流式播放模式选择新数据位置
                    var newDataPosition = IsStreamingMode ? STREAMING_NEW_DATA_X_POSITION : NORMAL_NEW_DATA_X_POSITION;
                    // 流动效果：最新数据在指定位置，旧数据向左移动
                    var baseX = newDataPosition - (_historyData.Count - 1 - i);
                    var random = new Random(analysis.Timestamp.GetHashCode());

                    // 按偏差值分组，为每个偏差值创建单独的散点图以实现不同颜色
                    var deviationGroups = analysis.DeviationPoints
                        .GroupBy(p => Math.Round(p.Deviation * 100, 2)) // 按偏差百分比分组，保留2位小数
                        .ToList();

                    foreach (var group in deviationGroups)
                    {
                        var xValues = new List<double>();
                        var yValues = new List<double>();
                        var deviationPercent = group.Key;

                        foreach (var point in group)
                        {
                            var xOffset = (random.NextDouble() - 0.5) * 0.8;
                            xValues.Add(baseX + xOffset);
                            yValues.Add(point.Deviation * 100);
                        }

                        if (xValues.Count > 0)
                        {
                            var scatter = _plot.Add.Scatter(xValues.ToArray(), yValues.ToArray());

                            // 根据偏差值设置颜色
                            var baseColor = GetDeviationColor(deviationPercent);

                            // 1秒更新间隔下的透明度渐变
                            var alpha = Math.Max(0.3, 0.9 - (i * 0.05));
                            scatter.Color = baseColor.WithAlpha(alpha);
                            scatter.MarkerSize = 6;
                            scatter.LineWidth = 0;
                            scatter.MarkerShape = MarkerShape.FilledCircle;
                            _scatterPlots.Add(scatter);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"重绘图表时发生错误: {ex.Message}");
                // 清理状态
                _scatterPlots.Clear();
                if (_plot != null)
                {
                    try
                    {
                        _plot.Clear();
                        // 重新添加零线
                        _zeroLine = _plot.Add.HorizontalLine(0);
                        _zeroLine.Color = Color.FromHex("#FCD535");
                        _zeroLine.LineWidth = 2;
                    }
                    catch (Exception clearEx)
                    {
                        Console.WriteLine($"清理图表时发生错误: {clearEx.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 更新坐标轴标签和范围
        /// </summary>
        private void UpdateAxisLabels()
        {
            if (_plot == null || _historyData.Count == 0)
                return;

            // 只有在用户没有手动缩放且不在流式播放模式时才自动调整X轴范围
            if (!_userHasZoomed && !IsStreamingMode)
            {
                // 固定显示窗口：从0到FLOW_WINDOW_SIZE
                var minX = 0.0;
                var maxX = FLOW_WINDOW_SIZE;
                _plot.Axes.SetLimitsX(minX, maxX);
            }

            // 自定义X轴刻度标签显示时间 - 流动效果
            var tickPositions = new List<double>();
            var tickLabels = new List<string>();

            // 根据数据点数量动态调整标签显示
            var maxLabels = Math.Min(8, _historyData.Count);
            var step = Math.Max(1, _historyData.Count / maxLabels);

            for (int i = 0; i < _historyData.Count; i += step)
            {
                var utcTime = _historyData[i].Timestamp.ToUniversalTime();
                // 根据流式播放模式选择新数据位置
                var newDataPosition = IsStreamingMode ? STREAMING_NEW_DATA_X_POSITION : NORMAL_NEW_DATA_X_POSITION;
                // 使用流动效果的X坐标
                var xPos = newDataPosition - (_historyData.Count - 1 - i);
                tickPositions.Add(xPos);
                tickLabels.Add(utcTime.ToString("HH:mm:ss"));
            }

            // 确保显示最后一个时间点（最新数据）
            if (_historyData.Count > 1)
            {
                var lastTime = _historyData.Last().Timestamp.ToUniversalTime();
                var newDataPosition = IsStreamingMode ? STREAMING_NEW_DATA_X_POSITION : NORMAL_NEW_DATA_X_POSITION;
                var lastXPos = newDataPosition;
                // 检查是否已经添加了最后一个点
                if (tickPositions.LastOrDefault() != lastXPos)
                {
                    tickPositions.Add(lastXPos);
                    tickLabels.Add(lastTime.ToString("HH:mm:ss"));
                }
            }

            if (tickPositions.Count > 0)
            {
                _plot.Axes.Bottom.TickGenerator = new ScottPlot.TickGenerators.NumericManual(
                    tickPositions.ToArray(), tickLabels.ToArray());
            }
        }

        /// <summary>
        /// 移除最旧的散点图（流水式更新时使用）
        /// </summary>
        private void RemoveOldestScatterPlot()
        {
            lock (_plotLock)
            {
                if (_plot == null || _scatterPlots.Count == 0)
                    return;

                try
                {
                    // 简化处理：当移除旧数据时，完全重绘
                    RedrawAllPlots();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"移除旧散点图时发生错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 手动刷新WPF图表控件（简化版本，避免递归调用）
        /// </summary>
        private void RefreshPlot()
        {
            try
            {
                if (_wpfPlot != null && _plot != null)
                {
                    // 检查当前是否已在UI线程
                    if (Application.Current.Dispatcher.CheckAccess())
                    {
                        // 已在UI线程，直接刷新
                        lock (_plotLock)
                        {
                            try
                            {
                                // 检查Plot状态是否正常
                                if (_plot.PlottableList != null)
                                {
                                    _wpfPlot.Refresh();
                                }
                            }
                            catch (InvalidOperationException ex)
                            {
                                Console.WriteLine($"刷新时发生集合修改异常: {ex.Message}");
                                // 不重试，避免递归调用
                            }
                        }
                    }
                    else
                    {
                        // 不在UI线程，使用Dispatcher调用（但不递归）
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            lock (_plotLock)
                            {
                                try
                                {
                                    if (_plot.PlottableList != null)
                                    {
                                        _wpfPlot.Refresh();
                                    }
                                }
                                catch (InvalidOperationException ex)
                                {
                                    Console.WriteLine($"刷新时发生集合修改异常: {ex.Message}");
                                }
                            }
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"刷新图表时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据偏差值计算颜色
        /// </summary>
        /// <param name="deviationPercent">偏差百分比 (-10 到 +10)</param>
        /// <returns>对应的颜色</returns>
        private Color GetDeviationColor(double deviationPercent)
        {
            // 限制偏差值在 ±10% 范围内
            var clampedDeviation = Math.Max(-MAX_DEVIATION_PERCENT, Math.Min(MAX_DEVIATION_PERCENT, deviationPercent));

            if (Math.Abs(clampedDeviation) < 0.01) // 接近零值，使用接近白色
            {
                return Color.FromHex("#F8F8FF");
            }

            if (clampedDeviation > 0) // 正偏差：白色到深绿色
            {
                var ratio = clampedDeviation / MAX_DEVIATION_PERCENT; // 0 到 1
                var level = (int)(ratio * COLOR_LEVELS);
                level = Math.Min(level, COLOR_LEVELS - 1);

                // 从浅绿到深绿的渐变
                var greenIntensity = (byte)(255 - (level * 255 / COLOR_LEVELS)); // 255 到 0
                var redBlue = (byte)(240 - (level * 240 / COLOR_LEVELS)); // 240 到 0

                var systemColor = System.Drawing.Color.FromArgb(redBlue, 255, redBlue);
                return Color.FromColor(systemColor);
            }
            else // 负偏差：白色到深红色
            {
                var ratio = Math.Abs(clampedDeviation) / MAX_DEVIATION_PERCENT; // 0 到 1
                var level = (int)(ratio * COLOR_LEVELS);
                level = Math.Min(level, COLOR_LEVELS - 1);

                // 从浅红到深红的渐变
                var redIntensity = 255;
                var greenBlue = (byte)(240 - (level * 240 / COLOR_LEVELS)); // 240 到 0

                var systemColor = System.Drawing.Color.FromArgb(redIntensity, greenBlue, greenBlue);
                return Color.FromColor(systemColor);
            }
        }

        /// <summary>
        /// 动态调整Y轴范围
        /// </summary>
        /// <param name="analysis">市场分析数据</param>
        private void UpdateYAxisRange(MarketAnalysis analysis)
        {
            if (_plot == null) return;

            // 计算当前数据的最大绝对偏差
            var maxAbsDeviation = 0.0;
            var extremeSymbols = 0;

            foreach (var point in analysis.DeviationPoints)
            {
                var absDeviation = Math.Abs(point.Deviation * 100); // 转换为百分比
                maxAbsDeviation = Math.Max(maxAbsDeviation, absDeviation);

                // 统计异常symbol数量
                if (absDeviation >= EXTREME_THRESHOLD)
                {
                    extremeSymbols++;
                }
            }

            // 更新异常symbol计数
            ExtremeSymbolsCount = extremeSymbols;

            // 确定新的Y轴范围
            double newYRange;
            string newMode;

            if (maxAbsDeviation <= NORMAL_Y_RANGE * 0.8) // 如果最大偏差小于正常范围的80%
            {
                // 保持正常模式
                newYRange = NORMAL_Y_RANGE;
                newMode = "正常模式 (±2%)";
                _isInExtremeMode = false;
            }
            else if (maxAbsDeviation >= AUTO_EXPAND_THRESHOLD) // 如果有超过5%的偏差
            {
                // 自动扩展模式
                var expandedRange = Math.Min(maxAbsDeviation * 1.2, MAX_AUTO_EXPAND_RANGE); // 增加20%缓冲
                newYRange = Math.Max(expandedRange, AUTO_EXPAND_THRESHOLD); // 至少5%
                newMode = $"扩展模式 (±{newYRange:F1}%)";
                _isInExtremeMode = true;
            }
            else if (extremeSymbols > 0) // 有异常但未达到自动扩展阈值
            {
                // 中等扩展模式
                newYRange = AUTO_EXPAND_THRESHOLD;
                newMode = $"警告模式 (±{newYRange:F1}%)";
                _isInExtremeMode = true;
            }
            else
            {
                // 保持当前范围或逐渐回归正常
                if (_currentYRange > NORMAL_Y_RANGE)
                {
                    newYRange = Math.Max(NORMAL_Y_RANGE, _currentYRange * 0.95); // 逐渐缩小
                    newMode = newYRange <= NORMAL_Y_RANGE ? "正常模式 (±2%)" : $"回归模式 (±{newYRange:F1}%)";
                    _isInExtremeMode = newYRange > NORMAL_Y_RANGE;
                }
                else
                {
                    newYRange = NORMAL_Y_RANGE;
                    newMode = "正常模式 (±2%)";
                    _isInExtremeMode = false;
                }
            }

            // 只有当范围发生显著变化且用户没有手动缩放Y轴时才更新Y轴
            if (Math.Abs(newYRange - _currentYRange) > 0.1 && !_userHasZoomed)
            {
                _currentYRange = newYRange;
                _plot.Axes.SetLimitsY(-_currentYRange, _currentYRange);

                // 更新零线位置（确保零线始终可见）
                if (_zeroLine != null)
                {
                    _zeroLine.Y = 0;
                }
            }
            else if (!_userHasZoomed)
            {
                // 即使范围没有显著变化，也要更新_currentYRange以保持状态同步
                _currentYRange = newYRange;
            }

            // 更新模式显示
            YAxisMode = newMode;
        }

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion

        public void Dispose()
        {
            _updateTimer?.Dispose();
            _marketDataService?.Dispose();
        }
    }
}
