﻿using System.Windows;
using System.Windows.Interop;
using System.Runtime.InteropServices;
using CryptoDeviationViewer.ViewModels;

namespace CryptoDeviationViewer;

/// <summary>
/// MainWindow的交互逻辑
/// </summary>
public partial class MainWindow : Window
{
    private MainViewModel _viewModel;

    // Windows API for dark title bar
    [DllImport("dwmapi.dll")]
    private static extern int DwmSetWindowAttribute(IntPtr hwnd, int attr, ref int attrValue, int attrSize);

    private const int DWMWA_USE_IMMERSIVE_DARK_MODE_BEFORE_20H1 = 19;
    private const int DWMWA_USE_IMMERSIVE_DARK_MODE = 20;

    public MainWindow()
    {
        InitializeComponent();

        // 设置暗黑标题栏
        SetDarkTitleBar();

        // 初始化ViewModel
        _viewModel = new MainViewModel();
        DataContext = _viewModel;

        // 窗口加载完成后初始化图表
        Loaded += MainWindow_Loaded;
        Closing += MainWindow_Closing;
    }

    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        // 初始化ScottPlot图表，传递WPF控件引用
        _viewModel.InitializePlot(ScottPlotControl.Plot, ScottPlotControl);
    }

    private void MainWindow_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
    {
        // 清理资源
        _viewModel?.StopDataUpdate();
        _viewModel?.Dispose();
    }

    /// <summary>
    /// 设置暗黑标题栏
    /// </summary>
    private void SetDarkTitleBar()
    {
        try
        {
            var hwnd = new WindowInteropHelper(this).Handle;
            if (hwnd == IntPtr.Zero)
            {
                // 如果窗口句柄还没有创建，在SourceInitialized事件中设置
                SourceInitialized += (s, e) =>
                {
                    var handle = new WindowInteropHelper(this).Handle;
                    ApplyDarkTitleBar(handle);
                };
            }
            else
            {
                ApplyDarkTitleBar(hwnd);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"设置暗黑标题栏失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 应用暗黑标题栏
    /// </summary>
    private void ApplyDarkTitleBar(IntPtr hwnd)
    {
        try
        {
            int useImmersiveDarkMode = 1;

            // 尝试新版本的属性
            var result = DwmSetWindowAttribute(hwnd, DWMWA_USE_IMMERSIVE_DARK_MODE, ref useImmersiveDarkMode, sizeof(int));

            // 如果失败，尝试旧版本的属性
            if (result != 0)
            {
                DwmSetWindowAttribute(hwnd, DWMWA_USE_IMMERSIVE_DARK_MODE_BEFORE_20H1, ref useImmersiveDarkMode, sizeof(int));
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"应用暗黑标题栏失败: {ex.Message}");
        }
    }
}