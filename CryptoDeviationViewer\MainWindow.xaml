﻿<Window x:Class="CryptoDeviationViewer.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CryptoDeviationViewer"
        xmlns:converters="clr-namespace:CryptoDeviationViewer.Converters"
        xmlns:scottplot="clr-namespace:ScottPlot.WPF;assembly=ScottPlot.WPF"
        mc:Ignorable="d"
        Title="加密货币偏差监控器" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen"
        Background="#1E1E1E">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:ExtremeCountToColorConverter x:Key="ExtremeCountToColorConverter"/>
        <converters:TestModeButtonTextConverter x:Key="TestModeButtonTextConverter"/>
        <converters:TestModeButtonColorConverter x:Key="TestModeButtonColorConverter"/>
        <converters:StreamingModeButtonTextConverter x:Key="StreamingModeButtonTextConverter"/>
        <converters:StreamingModeButtonColorConverter x:Key="StreamingModeButtonColorConverter"/>
    </Window.Resources>

    <Grid Background="#1E1E1E">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部状态栏 -->
        <Border Grid.Row="0" Background="#2D2D30" Padding="10" BorderBrush="#3F3F46" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                          Text="{Binding StatusText}"
                          Foreground="White"
                          FontSize="14"
                          VerticalAlignment="Center"/>

                <TextBlock Grid.Column="1"
                          Text="{Binding TotalSymbols, StringFormat='交易对: {0}'}"
                          Foreground="White"
                          FontSize="12"
                          Margin="20,0"
                          VerticalAlignment="Center"/>

                <TextBlock Grid.Column="2"
                          Text="{Binding AverageDeviation, StringFormat='平均偏差: {0:F3}%'}"
                          Foreground="White"
                          FontSize="12"
                          Margin="20,0"
                          VerticalAlignment="Center"/>

                <TextBlock Grid.Column="3"
                          Text="{Binding YAxisMode}"
                          Foreground="{Binding ExtremeSymbolsCount, Converter={StaticResource ExtremeCountToColorConverter}}"
                          FontSize="12"
                          Margin="20,0"
                          VerticalAlignment="Center"
                          FontWeight="Bold"/>

                <TextBlock Grid.Column="4"
                          Text="{Binding ExtremeSymbolsCount, StringFormat='异常: {0}'}"
                          Foreground="{Binding ExtremeSymbolsCount, Converter={StaticResource ExtremeCountToColorConverter}}"
                          FontSize="12"
                          Margin="20,0"
                          VerticalAlignment="Center"/>

                <TextBlock Grid.Column="5"
                          Text="{Binding LastUpdateTime, StringFormat='更新时间: {0}'}"
                          Foreground="White"
                          FontSize="12"
                          Margin="20,0"
                          VerticalAlignment="Center"/>

                <Button Grid.Column="6"
                        Content="{Binding IsStreamingMode, Converter={StaticResource StreamingModeButtonTextConverter}}"
                        Background="{Binding IsStreamingMode, Converter={StaticResource StreamingModeButtonColorConverter}}"
                        Foreground="White"
                        FontSize="11"
                        Padding="8,5"
                        Margin="10,0"
                        BorderThickness="1"
                        BorderBrush="#3F3F46"
                        Command="{Binding StreamingCommand}"
                        ToolTip="切换流式播放模式：新数据出现在倒数第二个位置，最右侧保留空白"
                        VerticalAlignment="Center"/>

                <Button Grid.Column="7"
                        Content="重置缩放"
                        Background="#4A4A4A"
                        Foreground="White"
                        FontSize="11"
                        Padding="8,5"
                        Margin="10,0"
                        BorderThickness="1"
                        BorderBrush="#3F3F46"
                        Command="{Binding ResetZoomCommand}"
                        ToolTip="重置图表缩放到默认视图"
                        VerticalAlignment="Center"/>

                <Button Grid.Column="8"
                        Content="{Binding IsTestMode, Converter={StaticResource TestModeButtonTextConverter}}"
                        Background="{Binding IsTestMode, Converter={StaticResource TestModeButtonColorConverter}}"
                        Foreground="White"
                        FontSize="11"
                        Padding="10,5"
                        Margin="10,0,0,0"
                        BorderThickness="1"
                        BorderBrush="#3F3F46"
                        Command="{Binding ToggleTestModeCommand}"
                        VerticalAlignment="Center"/>
            </Grid>
        </Border>

        <!-- 主要图表区域 -->
        <Border Grid.Row="1" Background="#1E1E1E" Margin="5" BorderBrush="#3F3F46" BorderThickness="1">
            <scottplot:WpfPlot x:Name="ScottPlotControl"
                              Background="#1E1E1E"/>
        </Border>

        <!-- 底部信息栏 -->
        <Border Grid.Row="2" Background="#2D2D30" Padding="10" BorderBrush="#3F3F46" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                          Text="X轴: 时间(秒) | Y轴: 偏差百分比(动态范围) | 颜色: 绿色(正偏差) 红色(负偏差) 白色(零偏差) | 黄线: 零偏差基准线"
                          Foreground="White"
                          FontSize="11"
                          VerticalAlignment="Center"/>

                <TextBlock Grid.Column="1"
                          Text="{Binding TestScenarioDescription}"
                          Foreground="Orange"
                          FontSize="11"
                          FontWeight="Bold"
                          Margin="20,0"
                          VerticalAlignment="Center"
                          Visibility="{Binding IsTestMode, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Ellipse Width="8" Height="8"
                            Fill="LightGreen"
                            VerticalAlignment="Center"
                            Visibility="{Binding IsRunning, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    <TextBlock Text="运行中"
                              Foreground="LightGreen"
                              FontSize="11"
                              Margin="5,0"
                              VerticalAlignment="Center"
                              Visibility="{Binding IsRunning, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
