using System.Globalization;
using System.Text;
using CryptoDeviationViewer.Models;

namespace CryptoDeviationViewer.Services
{
    /// <summary>
    /// CSV数据导出服务，用于调试和数据核对
    /// </summary>
    public static class CsvExportService
    {
        private static readonly string ExportDirectory = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DebugData");

        /// <summary>
        /// 确保导出目录存在
        /// </summary>
        private static void EnsureDirectoryExists()
        {
            if (!System.IO.Directory.Exists(ExportDirectory))
            {
                System.IO.Directory.CreateDirectory(ExportDirectory);
            }
        }

        /// <summary>
        /// 导出Premium Index原始数据
        /// </summary>
        /// <param name="premiumData">Premium Index数据列表</param>
        /// <param name="timestamp">数据获取时间戳</param>
        public static void ExportPremiumData(List<PremiumIndexData> premiumData, DateTime timestamp)
        {
            try
            {
                EnsureDirectoryExists();
                
                var fileName = $"PremiumIndex_{timestamp:yyyyMMdd_HHmmss}.csv";
                var filePath = System.IO.Path.Combine(ExportDirectory, fileName);

                var csv = new StringBuilder();
                
                // CSV头部
                csv.AppendLine("Symbol,MarkPrice,IndexPrice,EstimatedSettlePrice,LastFundingRate,InterestRate,NextFundingTime,Time,TimeUTC,DataAge(Minutes)");

                foreach (var item in premiumData)
                {
                    var dataTimestamp = DateTimeOffset.FromUnixTimeMilliseconds(item.Time).UtcDateTime;
                    var dataAge = (timestamp.ToUniversalTime() - dataTimestamp).TotalMinutes;
                    
                    csv.AppendLine($"{EscapeCsvField(item.Symbol)}," +
                                 $"{item.MarkPrice}," +
                                 $"{item.IndexPrice}," +
                                 $"{item.EstimatedSettlePrice}," +
                                 $"{item.LastFundingRate}," +
                                 $"{item.InterestRate}," +
                                 $"{item.NextFundingTime}," +
                                 $"{item.Time}," +
                                 $"{dataTimestamp:yyyy-MM-dd HH:mm:ss}," +
                                 $"{dataAge:F2}");
                }

                System.IO.File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
                Console.WriteLine($"Premium Index数据已导出: {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"导出Premium Index数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出Price原始数据
        /// </summary>
        /// <param name="priceData">Price数据列表</param>
        /// <param name="timestamp">数据获取时间戳</param>
        public static void ExportPriceData(List<PriceData> priceData, DateTime timestamp)
        {
            try
            {
                EnsureDirectoryExists();
                
                var fileName = $"Price_{timestamp:yyyyMMdd_HHmmss}.csv";
                var filePath = System.IO.Path.Combine(ExportDirectory, fileName);

                var csv = new StringBuilder();

                // CSV头部
                csv.AppendLine("Symbol,Price,Time,TimeUTC,DataAge(Minutes)");

                foreach (var item in priceData)
                {
                    var dataTimestamp = item.Time > 0 ? DateTimeOffset.FromUnixTimeMilliseconds(item.Time).UtcDateTime : DateTime.MinValue;
                    var dataAge = item.Time > 0 ? (timestamp.ToUniversalTime() - dataTimestamp).TotalMinutes : 0;

                    csv.AppendLine($"{EscapeCsvField(item.Symbol)}," +
                                 $"{item.Price}," +
                                 $"{item.Time}," +
                                 $"{(item.Time > 0 ? dataTimestamp.ToString("yyyy-MM-dd HH:mm:ss") : "N/A")}," +
                                 $"{dataAge:F2}");
                }

                System.IO.File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
                Console.WriteLine($"Price数据已导出: {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"导出Price数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出市场分析结果数据
        /// </summary>
        /// <param name="analysis">市场分析结果</param>
        public static void ExportMarketAnalysis(MarketAnalysis analysis)
        {
            try
            {
                EnsureDirectoryExists();
                
                var fileName = $"MarketAnalysis_{analysis.Timestamp:yyyyMMdd_HHmmss}.csv";
                var filePath = System.IO.Path.Combine(ExportDirectory, fileName);

                var csv = new StringBuilder();
                
                // CSV头部
                csv.AppendLine("Symbol,Timestamp,Deviation(%),DeviationRaw,Price,IndexPrice,PriceIndexRatio,AbsDeviation(%)");

                foreach (var point in analysis.DeviationPoints)
                {
                    var deviationPercent = point.Deviation * 100;
                    var priceIndexRatio = point.Price / point.IndexPrice;
                    var absDeviationPercent = Math.Abs(deviationPercent);
                    
                    csv.AppendLine($"{EscapeCsvField(point.Symbol)}," +
                                 $"{point.Timestamp:yyyy-MM-dd HH:mm:ss}," +
                                 $"{deviationPercent:F4}," +
                                 $"{point.Deviation:F6}," +
                                 $"{point.Price:F8}," +
                                 $"{point.IndexPrice:F8}," +
                                 $"{priceIndexRatio:F6}," +
                                 $"{absDeviationPercent:F4}");
                }

                // 添加统计信息到文件末尾
                csv.AppendLine();
                csv.AppendLine("=== 统计信息 ===");
                csv.AppendLine($"总交易对数量,{analysis.TotalSymbols}");
                csv.AppendLine($"平均偏差(%),{analysis.AverageDeviation * 100:F4}");
                csv.AppendLine($"标准差,{analysis.StandardDeviation:F6}");
                csv.AppendLine($"最大偏差(%),{analysis.MaxDeviation * 100:F4}");
                csv.AppendLine($"最小偏差(%),{analysis.MinDeviation * 100:F4}");
                csv.AppendLine($"正偏差数量,{analysis.PositiveDeviations}");
                csv.AppendLine($"负偏差数量,{analysis.NegativeDeviations}");

                System.IO.File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
                Console.WriteLine($"市场分析数据已导出: {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"导出市场分析数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出数据过滤日志
        /// </summary>
        /// <param name="filterLog">过滤日志信息</param>
        /// <param name="timestamp">时间戳</param>
        public static void ExportFilterLog(List<string> filterLog, DateTime timestamp)
        {
            try
            {
                EnsureDirectoryExists();
                
                var fileName = $"FilterLog_{timestamp:yyyyMMdd_HHmmss}.txt";
                var filePath = System.IO.Path.Combine(ExportDirectory, fileName);

                var content = new StringBuilder();
                content.AppendLine($"数据过滤日志 - {timestamp:yyyy-MM-dd HH:mm:ss}");
                content.AppendLine("=" + new string('=', 50));

                foreach (var log in filterLog)
                {
                    content.AppendLine(log);
                }

                System.IO.File.WriteAllText(filePath, content.ToString(), Encoding.UTF8);
                Console.WriteLine($"过滤日志已导出: {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"导出过滤日志失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 转义CSV字段中的特殊字符
        /// </summary>
        /// <param name="field">字段值</param>
        /// <returns>转义后的字段值</returns>
        private static string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return string.Empty;

            // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
            if (field.Contains(',') || field.Contains('"') || field.Contains('\n') || field.Contains('\r'))
            {
                return $"\"{field.Replace("\"", "\"\"")}\"";
            }

            return field;
        }
    }
}
