using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace CryptoDeviationViewer.Converters
{
    /// <summary>
    /// 根据异常symbol数量转换为对应的颜色
    /// </summary>
    public class ExtremeCountToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int count)
            {
                if (count == 0)
                {
                    return new SolidColorBrush(Colors.White); // 正常状态：白色
                }
                else if (count <= 5)
                {
                    return new SolidColorBrush(Colors.Yellow); // 轻微异常：黄色
                }
                else if (count <= 15)
                {
                    return new SolidColorBrush(Colors.Orange); // 中等异常：橙色
                }
                else
                {
                    return new SolidColorBrush(Colors.Red); // 严重异常：红色
                }
            }

            return new SolidColorBrush(Colors.White);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 测试模式按钮文本转换器
    /// </summary>
    public class TestModeButtonTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isTestMode)
            {
                return isTestMode ? "退出测试" : "测试模式";
            }
            return "测试模式";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 测试模式按钮颜色转换器
    /// </summary>
    public class TestModeButtonColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isTestMode)
            {
                return new SolidColorBrush(isTestMode ? Colors.DarkOrange : Colors.DarkSlateGray);
            }
            return new SolidColorBrush(Colors.DarkSlateGray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 流式播放模式按钮文本转换器
    /// </summary>
    public class StreamingModeButtonTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isStreamingMode)
            {
                return isStreamingMode ? "关闭流式播放" : "开启流式播放";
            }
            return "开启流式播放";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 流式播放模式按钮颜色转换器
    /// </summary>
    public class StreamingModeButtonColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isStreamingMode)
            {
                return new SolidColorBrush(isStreamingMode ? Colors.DarkGreen : Colors.DarkSlateBlue);
            }
            return new SolidColorBrush(Colors.DarkSlateBlue);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
